import { TrashIcon } from 'lucide-react'
import { useState } from 'react'
import { useRef } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { apiService } from '../services/api'

// Tipos e interfaces
type PaymentProvider = 'stripe' | 'google_play_billing' | 'apple_iap'
type Platform = 'web' | 'android' | 'ios'
type Period = 'monthly' | 'quarterly' | 'semiannual' | 'annual'

interface PaymentConfig {
  id: number
  payment_provider: PaymentProvider
  platform: Platform
  price?: number | string
  // period?: Period
  snaptokens?: number
  payment_provider_external_id: string
}

interface Plan {
  id: number
  name: string
  description: string
  price: string | number
  period: Period
  snaptokens: number
  affiliate_commission_percent: number
  affiliate_master_commission_percent: number
  isActive: boolean
  payment_config: PaymentConfig[]
}

// Componentes UI
const Button = ({ 
  children, 
  onClick, 
  variant = 'primary', 
  size = 'default',
  className = '',
  ...props 
}: { 
  children: React.ReactNode
  onClick?: () => void
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost'
  size?: 'default' | 'xs' | 'sm' | 'lg'
  className?: string
  [key: string]: any
}) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'
  
  const variantClasses = {
    primary: 'bg-indigo-600 text-white hover:bg-indigo-700',
    secondary: 'bg-white text-gray-900 border border-gray-300 hover:bg-gray-50',
    danger: 'bg-red-600 text-white hover:bg-red-700',
    ghost: 'bg-transparent text-gray-700 hover:bg-gray-100'
  }
  
  const sizeClasses = {
    default: 'px-4 py-2 text-sm',
    xs: 'px-1 py-1 text-xs',
    sm: 'px-3 py-1.5 text-xs',
    lg: 'px-6 py-3 text-base'
  }
  
  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  )
}

const Card = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => {
  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden flex flex-col ${className}`}>
      {children}
    </div>
  )
}

const CardHeader = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => {
  return (
    <div className={`px-6 py-4 border-b border-gray-200 ${className}`}>
      {children}
    </div>
  )
}

const CardTitle = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => {
  return (
    <h3 className={`text-lg font-semibold text-gray-900 ${className}`}>
      {children}
    </h3>
  )
}

const CardContent = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => {
  return (
    <div className={`p-6 ${className}`}>
      {children}
    </div>
  )
}

const CardFooter = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => {
  return (
    <div className={`px-6 py-4 border-t border-gray-200 bg-gray-50 mt-auto ${className}`}>
      {children}
    </div>
  )
}

const Input = ({ className = '', ...props }: React.InputHTMLAttributes<HTMLInputElement>) => {
  return (
    <input
      className={`block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${className}`}
      {...props}
    />
  )
}

const useCurrencyMask = () => {
  const inputRef = useRef<HTMLInputElement>(null);

  const formatBRL = (value: string) => {
    const onlyNumbers = value.replace(/\D/g, '');
    if (!onlyNumbers) return '';

    const valueBRL = (parseInt(onlyNumbers, 10) / 100).toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });

    return valueBRL;
  };

  const handleFocus = () => {
    if (inputRef.current) {
      setTimeout(() => {
        inputRef.current?.setSelectionRange(
          inputRef.current.value.length,
          inputRef.current.value.length
        );
      }, 0);
    }
  };

  return { inputRef, formatBRL, handleFocus };
};

const Textarea = ({ className = '', ...props }: React.TextareaHTMLAttributes<HTMLTextAreaElement>) => {
  return (
    <textarea
      className={`block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${className}`}
      {...props}
    />
  )
}

const Label = ({ children, className = '', html_for = '' }: { children: React.ReactNode, className?: string, html_for?: string }) => {
  return (
    <label className={`block text-sm font-medium text-gray-700 mb-1 ${className}`}
    {...html_for ? { htmlFor: html_for } : ''}
    >
      {children}
    </label>
  )
}

const Badge = ({ 
  children, 
  variant = 'primary',
  className = ''
}: { 
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'danger' | 'success'
  className?: string
}) => {
  const variantClasses = {
    primary: 'bg-indigo-100 text-indigo-800',
    secondary: 'bg-gray-100 text-gray-800',
    danger: 'bg-red-100 text-red-800',
    success: 'bg-green-100 text-green-800'
  }
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variantClasses[variant]} ${className}`}>
      {children}
    </span>
  )
}

const Modal = ({ 
  children, 
  onClose,
  className = ''
}: { 
  children: React.ReactNode
  onClose: () => void
  className?: string
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className={`bg-white rounded-lg shadow-xl overflow-hidden w-full max-w-md ${className}`}>
        {children}
      </div>
    </div>
  )
}

const PlusIcon = ({ className = '' }: { className?: string }) => (
  <svg className={`h-4 w-4 ${className}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
  </svg>
)

const EditIcon = ({ className = '' }: { className?: string }) => (
  <svg className={`h-4 w-4 ${className}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
  </svg>
)

const CheckIcon = ({ className = '' }: { className?: string }) => (
  <svg className={`h-4 w-4 ${className}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
  </svg>
)

const XIcon = ({ className = '' }: { className?: string }) => (
  <svg className={`h-4 w-4 ${className}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
)

const SettingsIcon = ({ className = '' }: { className?: string }) => (
  <svg className={`h-4 w-4 ${className}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
)

// Labels para exibição
const periodLabels: Record<Period, string> = {
  monthly: 'Mensal',
  quarterly: 'Trimestral',
  semiannual: 'Semestral',
  annual: 'Anual'
}

const providerLabels: Record<PaymentProvider, string> = {
  stripe: 'Stripe',
  google_play_billing: 'Google Play',
  apple_iap: 'Apple IAP'
}

const platformLabels: Record<Platform, string> = {
  web: 'Web',
  android: 'Android',
  ios: 'iOS'
}

// Componente principal
export function PlanManagement() {
  // const [plans, setPlans] = useState<Plan[]>([])
  /*
  [
    {
      id: 1,
      name: 'Plano Básico',
      price: '9,99',
      period: 'monthly',
      snaptokens: 100,
      affiliate_commission_percent: 10,
      affiliate_master_commission_percent: 5,
      description: 'Plano básico com recursos limitados',
      isActive: true,
      payment_config: [
        {
          id: 11,
          payment_provider: 'stripe',
          platform: 'web',
          payment_provider_external_id: 'price_123'
        }
      ]
    },
    {
      id: 2,
      name: 'Plano Premium',
      price: '19,99',
      period: 'monthly',
      snaptokens: 250,
      affiliate_commission_percent: 15,
      affiliate_master_commission_percent: 10,
      description: 'Plano premium com todos os recursos',
      isActive: true,
      payment_config: []
    }
  ]
  */

  const queryClient = useQueryClient()

  const fetchPlans = async () => {
    try {
      const response: any = await apiService.get('admin/plans');
      const responseData = response.data.map((plan: any) => ({
        ...plan,
        price: plan.price.toLocaleString('pt-BR', { minimumFractionDigits: 2 })
      }));
      return responseData;
    } catch (err) {
      console.error(err);
    }
  };

  const { data: plans, isLoading, isError } = useQuery({
    queryKey: ['plans'],
    queryFn: fetchPlans,
  })

  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isPaymentConfigModalOpen, setIsPaymentConfigModalOpen] = useState(false)
  const [currentPlan, setCurrentPlan] = useState<Plan | null>(null)
  const [currentPaymentConfig, setCurrentPaymentConfig] = useState<PaymentConfig | null>(null)
  
  const { inputRef, formatBRL, handleFocus } = useCurrencyMask();
  
  const [formData, setFormData] = useState<Omit<Plan, 'id' | 'payment_config'>>({
    name: '',
    price: '0,00',
    period: 'monthly',
    snaptokens: 0,
    affiliate_commission_percent: 0,
    affiliate_master_commission_percent: 0,
    description: '',
    isActive: true
  })

  const [paymentConfigForm, setPaymentConfigForm] = useState<Omit<PaymentConfig, 'id'>>({
    payment_provider: 'stripe',
    platform: 'web',
    price: undefined,
    snaptokens: undefined,
    // affiliate_commission_percent: undefined,
    // affiliate_master_commission_percent: undefined,
    payment_provider_external_id: ''
  })

  const addPlan = async (plan: Omit<Plan, 'id' | 'payment_config'>) => {
    const response = await apiService.post('admin/plans', plan);
    return response;
  }

  const mutation = useMutation({
    mutationFn: addPlan,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plans'] })
    },
  })

  const handleAddPlan = () => {
    const newPlan: Omit<Plan, 'id' | 'payment_config'> = {
      name: formData.name,
      description: formData.description,
      price: (typeof formData.price == "string") ? parseFloat(formData.price.replace(',', '.')) : formData.price,
      period: formData.period,
      snaptokens: formData.snaptokens,
      affiliate_commission_percent: formData.affiliate_commission_percent,
      affiliate_master_commission_percent: formData.affiliate_master_commission_percent,
      isActive: formData.isActive
    }
    mutation.mutate(newPlan)
    /*
    const newPlan: Plan = {
      ...formData,
      id: Date.now().toString(),
      payment_config: []
    }
    setPlans([...plans, newPlan])
    */
    setIsAddModalOpen(false)
    resetForm()
  }

  const updatePlan = async (plan: Plan) => {
    const response = await apiService.put(`admin/plans/${plan.id}`, plan);
    return response;
  }

  const updateMutation = useMutation({
    mutationFn: updatePlan,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plans'] })
    },
  })

  const handleEditPlan = () => {
    if (!currentPlan) return

    alert('Editing plan:', currentPlan)

    return false;

    const updatedPlan: Plan = {
      ...currentPlan,
      id: currentPlan.id,
      name: formData.name,
      description: formData.description,
      price: (typeof formData.price == "string") ? parseFloat(formData.price.replace(',', '.')) : formData.price,
      period: formData.period,
      snaptokens: formData.snaptokens,
      affiliate_commission_percent: formData.affiliate_commission_percent,
      affiliate_master_commission_percent: formData.affiliate_master_commission_percent,
      isActive: formData.isActive
    }

    updateMutation.mutate(updatedPlan)

    // invalidar cache
    queryClient.invalidateQueries({ queryKey: ['plans'] })

    /*
    const updatedPlans = plans?.map(plan => 
      plan.id === currentPlan.id ? { 
        ...formData, 
        id: currentPlan.id,
        payment_config: currentPlan.payment_config 
      } : plan
    )
    */
    // setPlans(updatedPlans)
    setIsEditModalOpen(false)
    resetForm()
  }

  const handleEditClick = (plan: Plan) => {
    setCurrentPlan(plan)
    setFormData({
      name: plan.name,
      price: plan.price, // BRL to decimal
      period: plan.period,
      snaptokens: plan.snaptokens,
      affiliate_commission_percent: plan.affiliate_commission_percent,
      affiliate_master_commission_percent: plan.affiliate_master_commission_percent,
      description: plan.description,
      isActive: plan.isActive
    })
    setIsEditModalOpen(true)
  }

  const handlePaymentConfigClick = (plan: Plan) => {
    setCurrentPlan(plan)
    setIsPaymentConfigModalOpen(true)
    setCurrentPaymentConfig(null)
    resetPaymentConfigForm()
  }

  const handleEditPaymentConfig = (config: PaymentConfig) => {
    setCurrentPaymentConfig(config)
    setPaymentConfigForm({
      payment_provider: config.payment_provider,
      platform: config.platform,
      price: config.price,
      snaptokens: config.snaptokens,
      payment_provider_external_id: config.payment_provider_external_id
    })
    setIsPaymentConfigModalOpen(true)
  }

  const handleSavePaymentConfig = () => {
    if (!currentPlan) return
    
    const configData = {
      ...paymentConfigForm,
      ...Object.fromEntries(
        Object.entries(paymentConfigForm)
          .filter(([_, v]) => v !== undefined)
      )
    }

    if (currentPaymentConfig) {
      const updatedPlans = plans?.map(plan => 
        plan.id === currentPlan.id ? { 
          ...plan, 
          payment_config: plan.payment_config.map(config => 
            config.id === currentPaymentConfig.id ? { ...config, ...configData } : config
          ) 
        } : plan
      )
      // setPlans(updatedPlans)
    } else {
      const newConfig: PaymentConfig = {
        ...configData,
        id: `${currentPlan.id}-${Date.now()}`
      }
      const updatedPlans = plans?.map(plan => 
        plan.id === currentPlan.id ? { 
          ...plan, 
          payment_config: [...plan.payment_config, newConfig] 
        } : plan
      )
      // setPlans(updatedPlans)
    }

    setIsPaymentConfigModalOpen(false)
    setCurrentPaymentConfig(null)
    resetPaymentConfigForm()
  }

  const handleDeletePaymentConfig = (configId: string) => {
    if (!currentPlan) return
    const updatedPlans = plans?.map(plan => 
      plan.id === currentPlan.id ? { 
        ...plan, 
        payment_config: plan.payment_config.filter(config => config.id !== configId) 
      } : plan
    )
    // setPlans(updatedPlans)
  }

  const handleDeletePlan = async (planId: string) => {
    // setPlans(plans.filter(plan => plan.id !== planId))
    await apiService.delete(`admin/plans/${planId}`);
    queryClient.invalidateQueries({ queryKey: ['plans'] });
  }

  const resetForm = () => {
    setFormData({
      name: '',
      price: '0,00',
      period: 'monthly',
      snaptokens: 0,
      affiliate_commission_percent: 0,
      affiliate_master_commission_percent: 0,
      description: '',
      isActive: true
    })
  }

  const resetPaymentConfigForm = () => {
    setPaymentConfigForm({
      payment_provider: 'stripe',
      platform: 'web',
      price: undefined,
      snaptokens: undefined,
      payment_provider_external_id: ''
    })
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement
    
    if (name === 'price') {
      const formattedValue = formatBRL(value);
      setFormData(prev => ({
        ...prev,
        [name]: formattedValue
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
               type === 'number' ? parseFloat(value) : value
      }))
    }
  }

  const handlePaymentConfigInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement
    
    if (name === 'price') {
      const formattedValue = formatBRL(value);
      setPaymentConfigForm(prev => ({
        ...prev,
        [name]: formattedValue
      }))
    } else {
      setPaymentConfigForm(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
               type === 'number' ? (value === '' ? 0 : parseFloat(value)) : 
               value === '' ? '' : value
      }))
    }
  }

  const getEffectiveValue = (plan: Plan, config: PaymentConfig, field: keyof Plan) => {
    return config[field] ?? plan[field]
  }

  const renderpayment_config = (plan: Plan) => {
    return plan.payment_config.map(config => (
      <div key={config.id} className="p-3 bg-gray-50 rounded-lg mb-2">
        <div className="flex justify-between items-start">
          <div>
            <span className="font-medium">{providerLabels[config.payment_provider]}</span>
            <span className="text-xs text-gray-500 ml-2">({platformLabels[config.platform]})</span>
          </div>
          <div className="flex space-x-2">
            <button 
              onClick={() => handleEditPaymentConfig(config)}
              className="text-indigo-600 hover:text-indigo-800 text-sm"
            >
              Editar
            </button>
            <button 
              onClick={() => handleDeletePaymentConfig(config.id)}
              className="text-red-600 hover:text-red-800 text-sm"
            >
              Remover
            </button>
          </div>
        </div>
        <div className="mt-2 grid grid-cols-2 gap-1 text-sm">
          <div>Preço: R$ {getEffectiveValue(plan, config, 'price')}</div>
          <div>Período: {periodLabels[getEffectiveValue(plan, config, 'period')]}</div>
          <div>Tokens: {getEffectiveValue(plan, config, 'snaptokens')}</div>
          <div></div>
          <div>Afiliado: {getEffectiveValue(plan, config, 'affiliate_commission_percent')}%</div>
          <div>Master: {getEffectiveValue(plan, config, 'affiliate_master_commission_percent')}%</div>
          <div className="col-span-2 truncate">
            <span className="text-gray-500">ID:</span> {config.payment_provider_external_id}
          </div>
        </div>
      </div>
    ))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Planos de Assinatura</h1>
          <Button onClick={() => setIsAddModalOpen(true)}>
            <PlusIcon className="mr-2" /> Adicionar Plano
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {plans?.map(plan => (
            <Card key={plan.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle>{plan.name}</CardTitle>
                  <Badge variant={plan.is_active ? 'success' : 'danger'}>
                    {plan.is_active ? 'Ativo' : 'Inativo'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2">
                    <div>Preço: R$ {plan.price}</div>
                    <div>Período: {periodLabels[plan.period]}</div>
                    <div>Tokens: {plan.snaptokens}</div>
                  </div>
                    <div className="grid grid-cols-2 gap-2">
                      <div>Afiliado: {plan.affiliate_commission_percent}%</div>
                      <div>Master: {plan.affiliate_master_commission_percent}%</div>
                    </div>
                  <p className="text-sm text-gray-600">{plan.description}</p>
                  
                  <div className="pt-4 border-t border-gray-100">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Configurações de Pagamento</h4>
                    {plan.payment_config.length > 0 ? (
                      renderpayment_config(plan)
                    ) : (
                      <p className="text-sm text-gray-500 italic">Nenhuma configuração</p>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex items-center justify-between">
                <div className="flex space-x-2">
                  <span
                  onClick={() => handleEditClick(plan)}
                  >
                  <Button 
                    variant="secondary" 
                    size="xs"                    
                  >
                    <EditIcon className="mr-2" /> Editar
                  </Button>
                  </span>
                  <button 
                    onClick={() => {
                      if (confirm(`Tem certeza que deseja remover o plano ${plan.name}?`)) {
                        handleDeletePlan(plan.id)
                      }
                    }}
                    className="cursor-pointer text-red-600 hover:text-red-800 text-sm"
                  >
                    <TrashIcon className="mr-2" size={16} />
                  </button>
                </div>
                <Button 
                  variant="ghost" 
                  size="xs"
                  onClick={() => handlePaymentConfigClick(plan)}
                >
                  <SettingsIcon className="mr-2" /> Config. Pagamento
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>

      {/* Modal Adicionar/Editar Plano */}
      {(isAddModalOpen || isEditModalOpen) && (
        <Modal onClose={() => {
          setIsAddModalOpen(false)
          setIsEditModalOpen(false)
          resetForm()
        }}>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>{isEditModalOpen ? 'Editar Plano' : 'Adicionar Plano'}</CardTitle>
              <button onClick={() => {
                setIsAddModalOpen(false)
                setIsEditModalOpen(false)
                resetForm()
              }}>
                <XIcon />
              </button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label>Nome do Plano</Label>
                <Input
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Preço (R$)</Label>
                  <Input
                    name="price"
                    type="text"
                    value={formData.price}
                    onChange={handleInputChange}
                    onFocus={handleFocus}
                    ref={inputRef}
                  />
                </div>
                <div>
                  <Label>Período</Label>
                  <select
                    name="period"
                    value={formData.period}
                    onChange={handleInputChange}
                    className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  >
                    <option value="monthly">Mensal</option>
                    <option value="quarterly">Trimestral</option>
                    <option value="semiannual">Semestral</option>
                    <option value="annual">Anual</option>
                  </select>
                </div>
              </div>

                <div>
                  <Label>Snap Tokens</Label>
                  <Input
                    name="snaptokens"
                    type="number"
                    value={formData.snaptokens}
                    onChange={handleInputChange}
                    min="0"
                  />
                </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Comissão Afiliado (%)</Label>
                  <Input
                    name="affiliate_commission_percent"
                    type="number"
                    value={formData.affiliate_commission_percent}
                    onChange={handleInputChange}
                    min="0"
                    max="100"
                  />
                </div>
                <div>
                  <Label>Comissão Master (%)</Label>
                  <Input
                    name="affiliate_master_commission_percent"
                    type="number"
                    value={formData.affiliate_master_commission_percent}
                    onChange={handleInputChange}
                    min="0"
                    max="100"
                  />
                </div>
              </div>
              
              <div>
                <Label>Descrição</Label>
                <Textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="flex items-center">
                <input
                  id="isActive"
                  name="isActive"
                  type="checkbox"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <Label html_for="isActive" className="ml-2">Plano Ativo</Label>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-3">
            <Button 
              variant="secondary" 
              onClick={() => {
                setIsAddModalOpen(false)
                setIsEditModalOpen(false)
                resetForm()
              }}
            >
              Cancelar
            </Button>
            <Button onClick={isEditModalOpen ? handleEditPlan : handleAddPlan}>
              <CheckIcon className="mr-2" />
              {isEditModalOpen ? 'Atualizar Plano' : 'Adicionar Plano'}
            </Button>
          </CardFooter>
        </Modal>
      )}

      {/* Modal Configuração de Pagamento */}
      {isPaymentConfigModalOpen && currentPlan && (
        <Modal onClose={() => {
          setIsPaymentConfigModalOpen(false)
          setCurrentPaymentConfig(null)
          resetPaymentConfigForm()
        }}>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>
                {currentPaymentConfig ? 'Editar Configuração' : 'Adicionar Configuração'}
                <div className="text-sm font-normal text-gray-500 mt-1">
                  Plano: {currentPlan.name}
                </div>
              </CardTitle>
              <button onClick={() => {
                setIsPaymentConfigModalOpen(false)
                setCurrentPaymentConfig(null)
                resetPaymentConfigForm()
              }}>
                <XIcon />
              </button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label>Provedor de Pagamento</Label>
                <select
                    name="payment_provider"
                    value={paymentConfigForm.payment_provider}
                    onChange={handlePaymentConfigInputChange}
                    className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  >
                    <option value="stripe">Stripe</option>
                    <option value="google_play_billing">Google Play</option>
                    <option value="apple_iap">Apple IAP</option>
                  </select>
              </div>

              <div>
                <Label>Plataforma</Label>
                <select
                  name="platform"
                  value={paymentConfigForm.platform}
                  onChange={handlePaymentConfigInputChange}
                  className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="web">Web</option>
                  <option value="android">Android</option>
                  <option value="ios">iOS</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Preço (R$)</Label>
                  <Input
                    name="price"
                    type="text"
                    value={paymentConfigForm.price !== undefined ? formatBRL(String(paymentConfigForm.price)) : ''}
                    onChange={handlePaymentConfigInputChange}
                    onFocus={handleFocus}
                    ref={inputRef}
                    placeholder={`Padrão: ${currentPlan.price}`}
                  />
                </div>
                <div>
                  <Label>Período</Label>
                  <select
                    name="period"
                    value={paymentConfigForm.period ?? ''}
                    onChange={handlePaymentConfigInputChange}
                    className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  >
                    <option value="">Padrão: {periodLabels[currentPlan.period]}</option>
                    <option value="monthly">Mensal</option>
                    <option value="quarterly">Trimestral</option>
                    <option value="semiannual">Semestral</option>
                    <option value="annual">Anual</option>
                  </select>
                </div>
              </div>

                <div>
                  <Label>Snap Tokens</Label>
                  <Input
                    name="snaptokens"
                    type="number"
                    value={paymentConfigForm.snaptokens ?? ''}
                    onChange={handlePaymentConfigInputChange}
                    min="0"
                    placeholder={`Padrão: ${currentPlan.snaptokens}`}
                  />
                </div>


              <div>
                <Label>ID Externo do Provedor *</Label>
                <Input
                  name="payment_provider_external_id"
                  value={paymentConfigForm.payment_provider_external_id}
                  onChange={handlePaymentConfigInputChange}
                  placeholder="Ex: price_123, com.example.product"
                  required
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            {currentPaymentConfig && (
              <Button 
                variant="danger" 
                onClick={() => {
                  if (confirm('Tem certeza que deseja remover esta configuração?')) {
                    handleDeletePaymentConfig(currentPaymentConfig.id)
                    setIsPaymentConfigModalOpen(false)
                  }
                }}
              >
                Remover
              </Button>
            )}
            <div className="flex space-x-3">
              <Button 
                variant="secondary" 
                onClick={() => {
                  setIsPaymentConfigModalOpen(false)
                  setCurrentPaymentConfig(null)
                  resetPaymentConfigForm()
                }}
              >
                Cancelar
              </Button>
              <Button 
                onClick={handleSavePaymentConfig}
                disabled={!paymentConfigForm.payment_provider_external_id}
              >
                {currentPaymentConfig ? 'Salvar Alterações' : 'Adicionar Configuração'}
              </Button>
            </div>
          </CardFooter>
        </Modal>
      )}
    </div>
  )
}