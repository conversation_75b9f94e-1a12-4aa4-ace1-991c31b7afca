import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import {
  getAdminSubscriptions,
  cancelAdminSubscription,
  updateSubscriptionStatus,
  SubscriptionFilters,
  AdminSubscriptionsResponse
} from '../services/adminSubscriptions';

/**
 * Hook para gerenciar assinaturas administrativas
 * @returns Objeto com dados das assinaturas e funções para interagir com elas
 */
export const useAdminSubscriptions = () => {
  const queryClient = useQueryClient();
  
  // Estados para filtros
  const [filters, setFilters] = useState<SubscriptionFilters>({
    page: 1,
    limit: 10,
    status: '',
    platform: '',
    search: ''
  });

  // Query para buscar assinaturas
  const {
    data: subscriptionsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['adminSubscriptions', filters],
    queryFn: () => getAdminSubscriptions(filters),
    keepPreviousData: true, // Mantém dados anteriores durante carregamento
    staleTime: 30 * 1000, // 30 segundos
  });

  // Mutation para cancelar assinatura
  const cancelMutation = useMutation({
    mutationFn: ({ subscriptionId, immediate }: { subscriptionId: string; immediate: boolean }) =>
      cancelAdminSubscription(subscriptionId, immediate),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminSubscriptions'] });
      toast.success('Assinatura cancelada com sucesso!', {
        position: 'bottom-right'
      });
    },
    onError: (error) => {
      console.error('Erro ao cancelar assinatura:', error);
      toast.error('Erro ao cancelar assinatura. Tente novamente.', {
        position: 'bottom-right'
      });
    }
  });

  // Mutation para atualizar status
  const updateStatusMutation = useMutation({
    mutationFn: ({ subscriptionId, status }: { subscriptionId: string; status: string }) =>
      updateSubscriptionStatus(subscriptionId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminSubscriptions'] });
      toast.success('Status da assinatura atualizado com sucesso!', {
        position: 'bottom-right'
      });
    },
    onError: (error) => {
      console.error('Erro ao atualizar status:', error);
      toast.error('Erro ao atualizar status. Tente novamente.', {
        position: 'bottom-right'
      });
    }
  });

  /**
   * Atualiza os filtros de busca
   * @param newFilters Novos filtros
   */
  const updateFilters = (newFilters: Partial<SubscriptionFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      // Reset para primeira página quando mudamos filtros (exceto paginação)
      ...(newFilters.page === undefined && { page: 1 })
    }));
  };

  /**
   * Cancela uma assinatura
   * @param subscriptionId ID da assinatura
   * @param immediate Se deve cancelar imediatamente
   */
  const cancelSubscription = (subscriptionId: string, immediate: boolean = false) => {
    cancelMutation.mutate({ subscriptionId, immediate });
  };

  /**
   * Atualiza o status de uma assinatura
   * @param subscriptionId ID da assinatura
   * @param status Novo status
   */
  const updateStatus = (subscriptionId: string, status: string) => {
    updateStatusMutation.mutate({ subscriptionId, status });
  };

  /**
   * Vai para a próxima página
   */
  const nextPage = () => {
    if (subscriptionsData?.pagination && filters.page! < Math.ceil(subscriptionsData.pagination.total / filters.limit!)) {
      updateFilters({ page: (filters.page || 1) + 1 });
    }
  };

  /**
   * Vai para a página anterior
   */
  const previousPage = () => {
    if (filters.page! > 1) {
      updateFilters({ page: (filters.page || 1) - 1 });
    }
  };

  /**
   * Vai para uma página específica
   * @param page Número da página
   */
  const goToPage = (page: number) => {
    updateFilters({ page });
  };

  return {
    // Dados
    subscriptions: subscriptionsData?.data || [],
    pagination: subscriptionsData?.pagination,
    filters,
    
    // Estados
    isLoading,
    error,
    isCanceling: cancelMutation.isPending,
    isUpdatingStatus: updateStatusMutation.isPending,
    
    // Funções
    updateFilters,
    cancelSubscription,
    updateStatus,
    refetch,
    nextPage,
    previousPage,
    goToPage
  };
};
