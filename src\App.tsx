import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { LoginPage } from './pages/LoginPage';
import { AuthRouter } from './routes/AuthRouter';
import { ActiveWorkoutPage } from './pages/ActiveWorkoutPage';
import { AddMealPage } from './pages/AddMealPage';
import { WorkoutSelectionPage } from './pages/WorkoutSelectionPage';
// import { PointsProvider } from './contexts/PointsContext';
import PreLoginRegisterPage from './pages/PreLoginRegisterPage';
import PreLoginForgotPasswordPage from './pages/PreLoginForgotPasswordPage';
import { ActiveWorkoutPageMockup } from './pages/ActiveWorkoutPageMockup';
import DevPrompt from './pages/DevPrompt';
import Join from './pages/panels/affiliates/Join';
import Invite from './pages/panels/affiliates/Invite';

// <PointsProvider></PointsProvider>

function App() {
  return (
      <BrowserRouter
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true,
      }}
      >
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<PreLoginRegisterPage />} />
          <Route path="/forgot-password" element={<PreLoginForgotPasswordPage />} />

          <Route path="/affiliates/join" element={<Join />} />
          <Route path="/invite" element={<Invite />} />

          <Route path="/workout/active/:workoutId" element={<ActiveWorkoutPage />} />
          <Route path="/workout/active/mockup/:workoutId" element={<ActiveWorkoutPageMockup />} />
          <Route path="/workout/select" element={<WorkoutSelectionPage />} />
          <Route path="/diary/add-meal" element={<AddMealPage />} />
          <Route path="/dev/prompt" element={<DevPrompt />} />
          <Route path="/*" element={<AuthRouter />} />
        </Routes>
      </BrowserRouter>
  );
}

export default App;