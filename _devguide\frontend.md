# Guia de Frontend

## Princípios Gerais

* Projeto em **React + TypeScript** com Vite
* Uso de **Tailwind CSS** para estilização
* Requisições via **React Query** e serviços externos organizados
* Componentização limpa e separação de responsabilidades
* Adoção dos princípios **SOLID** e boas práticas de **Clean Code**


## 
🌟
 UI e Experiência

* Uso extensivo de Tailwind para classes utilitárias
* Responsividade com `flex`, `grid` e `media queries`
* Interfaces limpas, com espaçamentos adequados e foco em legibilidade
* Componentes reutilizáveis com props bem definidos

## 
🤖
 Diretrizes para a IA

* Separar lógica e visual sempre que possível
* Gerar hooks para chamadas com React Query (`useCreateTask`, `useListTasks`)
* Componentes puros devem ser enxutos, sem acesso direto à API
* Sugestão de responsividade e semântica no HTML gerado