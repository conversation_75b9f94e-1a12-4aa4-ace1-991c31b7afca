import { useEffect, useState } from "react";
import { AppLogo2 } from "../components/AppLogo2";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { Link } from "react-router-dom";
import { useForm } from "react-hook-form";
import axios from "axios";
import { toast, ToastContainer } from "react-toastify";
import Loader from 'rsuite/Loader';
import 'rsuite/Loader/styles/index.css';

interface RegisterData {
    name: string;
    email: string;
    password: string;
    password_confirm: string;
    invite?: string;
}

const storeAction = (data: any) => {
  console.log("Dados recebidos:", data);

  const access_token = data.access_token;
  const refresh_token = data.refresh_token;
  const device_uid = data.device_uid;

  if (!access_token || !refresh_token || !device_uid) {
    console.error("Algum dado esperado está faltando!");
    return;
  }

  console.log(data);

  localStorage.setItem('accessToken', access_token);
  localStorage.setItem('refreshToken', refresh_token);
  localStorage.setItem('deviceUid', device_uid);
  localStorage.setItem('preauth', 'true');

  // /login
  window.location.href = '/login';
};

const PreLoginRegisterPage = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);
    const [loading, setLoading] = useState(false);
    const [initialInvite, setInitialInvite] = useState('');
    
    useEffect(() => {
      const snapfitData = localStorage.getItem('snapfit_data');
      if (snapfitData) {
        try {
          const data = JSON.parse(snapfitData);
          if (data.invite) {
            setInitialInvite(data.invite);
          }
        } catch (e) {
          console.error('Error parsing snapfit_data:', e);
        }
      }
    }, []);

    // await 3 seconds
    setTimeout(() => {
      setLoading(false);
    }, 3000);

    const [password, setPassword] = useState("");

    const { register, handleSubmit, formState: { errors } } = useForm<RegisterData>();

    const submitForm = async (formData: RegisterData) => {
      setLoading(true);
      const api_url = import.meta.env.VITE_API_APP_URL+'/auth/register';
      try {
        const response = await axios.post(api_url, formData);
        if (response.data.status === 'success') {
          storeAction(response.data.data);
        }
      } catch (err: any) {
        setLoading(false);
        const errors = err.response.data.errors;

        errors.forEach((error: string) => {
        toast.error(error, {
          position: "bottom-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          });
        });
      }
    }

    return (
  <>
<div className="bg-gray-50 font-[sans-serif]">
  
  <div className="min-h-screen flex flex-col items-center justify-center py-6 px-4">
    
    <div className="max-w-md w-full">
      <div className="p-8 rounded-2xl bg-white shadow">
      {loading && <Loader backdrop center size="sm" className="z-50" />}

        <h2 className="flex items-center justify-center">
          <AppLogo2 />
        </h2>
        <h3 className="text-center mt-4 font- text-gray-800 text-lg mb-2 block">
          Crie sua conta
        </h3>
        <form className="mt-8 space-y-4" onSubmit={handleSubmit(submitForm)}>
          <div>
            <label className="text-gray-800 text-sm mb-2 block sr-only">
              Nome
            </label>
            <div className="relative flex items-center">
              <input
                type="text"
                required={true}
                className="w-full text-gray-800 text-sm border border-gray-300 px-4 py-3 rounded-md outline-blue-600"
                placeholder="Nome"
                {...register("name", {
                    required: "O Nome é obrigatório",
                })}
              />              
            </div>            
          { errors.name && <div className="text-red-500 text-sm">{errors.name.message}</div> }
          </div>

          <div>
            <label className="text-gray-800 text-sm mb-2 block sr-only">
              E-mail
            </label>
            <div className="relative flex items-center">
              <input
                type="email"
                required={true}
                className="w-full text-gray-800 text-sm border border-gray-300 px-4 py-3 rounded-md outline-blue-600"
                placeholder="E-mail"
                {...register("email", {
                    required: "O E-mail é obrigatório",
                    pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "O E-mail deve ser válido"
                    }
                })}
              />
              
            </div>            
          { errors.email && <div className="text-red-500 text-sm">{errors.email.message}</div> }
          </div>

          <div>
            <label className="text-gray-800 text-sm mb-2 block sr-only">Senha</label>
            <div className="relative flex items-center">
              <input
                type={showPassword ? "text" : "password"}
                required={true}
                className="w-full text-gray-800 text-sm border border-gray-300 px-4 py-3 rounded-md outline-blue-600"
                placeholder="Senha"
                {...register("password", {
                    required: "A Senha é obrigatória",
                    minLength: {
                        value: 6,
                        message: "A Senha deve ter pelo menos 6 caracteres"
                    }
                })}
                onChange={(e) => setPassword(e.target.value)}
              />
              { showPassword ? (
                <EyeIcon
                className="w-4 h-4 absolute right-4 cursor-pointer text-gray-400"
                onClick={() => setShowPassword(!showPassword)}
                />
              ) : (
                <EyeOffIcon
                className="w-4 h-4 absolute right-4 cursor-pointer text-gray-400"
                onClick={() => setShowPassword(!showPassword)}
                />
              )}
            </div>
            { errors.password && <div className="text-red-500 text-sm">{errors.password.message}</div> }
          </div>

         <div>
            <label className="text-gray-800 text-sm mb-2 block sr-only">Confirmar Senha</label>
            <div className="relative flex items-center">
              <input
                type={showPasswordConfirm ? "text" : "password"}
                required={true}
                className="w-full text-gray-800 text-sm border border-gray-300 px-4 py-3 rounded-md outline-blue-600"
                placeholder="Confirmar Senha"
                {...register("password_confirm", {
                    required: "A Confirmação de Senha é obrigatória",
                    validate: value => value === password || "As Senhas devem ser iguais"
                })}
              />
              
              { showPasswordConfirm ? (
                <EyeIcon
                className="w-4 h-4 absolute right-4 cursor-pointer text-gray-400"
                onClick={() => setShowPasswordConfirm(!showPasswordConfirm)}
                />
              ) : (
                <EyeOffIcon
                className="w-4 h-4 absolute right-4 cursor-pointer text-gray-400"
                onClick={() => setShowPasswordConfirm(!showPasswordConfirm)}
                />
              )}
            </div>
            { errors.password_confirm && <div className="text-red-500 text-sm">{errors.password_confirm.message}</div> }
          </div>

          <hr className="border-gray-300 my-4" />
          
          <div>
            <label className="text-gray-500 text-sm mb-2 block">
              Possui um código promocional?
            </label>
            <div className="relative flex items-center">
              <input
                type="text"
                className="w-60 text-gray-800 text-sm border border-gray-300 px-4 py-3 rounded-md outline-blue-600"
                placeholder="Código Promocional (Opcional)"
                defaultValue={initialInvite}
                {...register("invite")}
              />
            </div>
          </div>
          
          
          



          

          <div className="!mt-4">
            <button
              type="submit"
              className="w-full py-3 px-4 text-sm tracking-wide rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none"
            >
              Continuar
            </button>
          </div>
          <p className="text-gray-800 text-sm !mt-4 text-right">
            <Link
              to="/login"
              className="text-blue-600 hover:underline ml-1 whitespace-nowrap"
            >
              Voltar
            </Link>
          </p>
          

        </form>
      </div>
    </div>
  </div>
</div>

</>
)
};

export default PreLoginRegisterPage;