import React, { useEffect } from 'react';
import { useNavigate, Navigate } from 'react-router-dom';
import { UserIcon, ShieldIcon, DumbbellIcon, AppleIcon } from 'lucide-react';
import { setAuth, getAuth } from '../services/auth';
import { mockUsers } from '../services/mockAuth';
import type { User as UserType, UserRole } from '../types/user';
import PreLoginPage from './PreLoginPage';
import axiosInstance from '../axiosInstance';
import { AppLogo2 } from '../components/AppLogo2';
import { apiService } from '../services/api';

const userOptions: { role: UserRole; icon: React.ReactNode; label: string; description: string }[] = [
  {
    role: 'user',
    icon: <UserIcon className="w-6 h-6" />,
    label: 'Usuário',
    description: 'Acesse seus treinos e dietas'
  },
  {
    role: 'admin',
    icon: <ShieldIcon className="w-6 h-6" />,
    label: 'Administrador',
    description: '<PERSON>ere<PERSON>ie usuários e configurações'
  },
  {
    role: 'coach',
    icon: <DumbbellIcon className="w-6 h-6" />,
    label: 'Coach',
    description: 'Gerencie treinos dos seus alunos'
  },
  {
    role: 'nutritionist',
    icon: <AppleIcon className="w-6 h-6" />,
    label: 'Nutricionista',
    description: 'Gerencie dietas dos seus pacientes'
  }
];


export function LoginPage() {
  const navigate = useNavigate();
  const auth = getAuth();
  const [error, setError] = React.useState<string | null>(null);

  const [userExtraOptions, setUserExtraOptions] = React.useState<any>({
    affiliates: false,
    admin: false
  });

  // Show login form if not already logged in
  const preauth = localStorage.getItem('preauth');
  if (!preauth) {
    return <PreLoginPage />;
  }

  const addAff = async (affData: any) => {
    try {
      const response: any = await apiService.post('users/affiliate/join', affData);

      // remove aff_join and aff_ref from snapfit_data
      const snapfitData = localStorage.getItem('snapfit_data');
      if (snapfitData) {
        const snapfitDataParsed = JSON.parse(snapfitData);
        if (snapfitDataParsed?.aff_join)  delete snapfitDataParsed.aff_join;
        if (snapfitDataParsed?.aff_ref)  delete snapfitDataParsed.aff_ref;
        localStorage.setItem('snapfit_data', JSON.stringify(snapfitDataParsed));
      }

      setUserExtraOptions((prev: any) => ({
        ...prev,
        affiliate: 'pending'
      }));

      return response.data;
    } catch (err) {
      console.error('Error adding affiliate:', err);
    }
    return false;
  }

  useEffect(() => {
    const snapfit_data = localStorage.getItem('snapfit_data');
    if (snapfit_data) {
      const snapfitData = JSON.parse(snapfit_data);
      if (snapfitData.aff_join) {
        const aff_data = {
          aff_ref: snapfitData?.aff_ref || null
        }
        addAff(aff_data);
      }
    }
  }, []);


  const getUserOptions = async () => {
    try {
      const response: any = await apiService.get('users/options');
      const userOptions = response.data;
      if (userOptions) {
        setUserExtraOptions((prev: any) => ({
          ...prev,
          affiliate: userOptions.affiliate,
          admin: userOptions.admin
        }));
      }
      return true;
    } catch (err) {
      console.error('Error getting user options:', err);
    }
    return false;
  }

  useEffect(() => {
    // get User Options
    getUserOptions();
  }, [])

  // Redirect if already logged in
  if (auth.isAuthenticated) {
    switch (auth.user?.role) {
      case 'admin':
        return <Navigate to="/admin" replace />;
      case 'coach':
      case 'nutritionist':
        return <Navigate to="/professional" replace />;
      default:
        return <Navigate to="/" replace />;
    }
  }

  const handleRole = async (role: string) => {
    const rolesRoute = `/auth/role/${role}`;
    const checkRole = await axiosInstance.get(rolesRoute);

    if (checkRole.data?.status === "error") {
        return false;
    }
    return true;
  }

  const handleLogin = async (role: UserRole) => {
    const user = mockUsers[role];
    if (!user) {
      setError(`Erro ao fazer login como ${role}`);
      return;
    }

    try {
      setAuth('mock-token', user);

      
      const rolesAlloweds = ['admin', 'coach', 'nutritionist', 'user'];
      if (!rolesAlloweds.includes(role)) {
        alert('Invalid role');
        return;
      }      

      const isRoleAllowed = await handleRole(role);
      if (!isRoleAllowed) {
        alert('Invalid role');
        return;
      }

    
      // Redirect based on role
      switch (role) {
        case 'admin':
          navigate('/admin');
          break;
        case 'coach':
        case 'nutritionist':
          navigate('/professional');
          break;
        default:
          navigate('/');
      }
    } catch (err) {
      setError('Erro ao fazer login. Tente novamente.');
      console.error('Login error:', err);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-blue-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="flex flex-col gap-4 items-center text-center mb-8">
          <AppLogo2 />
          <p className="text-gray-600">Selecione seu tipo de acesso para continuar</p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-lg text-sm">
            {error}
          </div>
        )}

        {userExtraOptions.affiliate && (
        <div
        className={`${userExtraOptions.affiliate == 'pending' ? 'opacity-50 cursor-default pointer-events-none' : ''}
        mb-4`}
        >
        <button
          onClick={() => 
          {
            if (userExtraOptions.affiliates == 'pending') return;
            navigate('/affiliates');
          }
          }
          className="bg-white p-6 w-full rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 text-left group"
        >        
          <div className="flex items-center gap-4 mb-2">
            <div className="p-2 bg-indigo-50 rounded-lg text-indigo-600 group-hover:bg-indigo-100 transition-colors">
              <UserIcon className="w-6 h-6" />
            </div>
            <span className="font-medium text-gray-800">Afiliado</span>
            {userExtraOptions.affiliate == 'pending' && (
            <span className="text-sm text-white bg-red-600 px-2 py-1 rounded-full">Pendente</span>
            )}
            </div>
          <p className="text-sm text-gray-600">Gerencie sua conta de afiliado</p>
        </button>
        </div>
        )}

        <div className="grid grid-cols-1 gap-4">
          {userOptions.map(({ role, icon, label, description }) => (
            <button
              key={role}
              onClick={() => handleLogin(role)}
              className={`
                ${role=== 'admin' && !userExtraOptions.admin ? 'hidden' : ''}
                bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 text-left group`}
            >
              <div className="flex items-center gap-4 mb-2">
                <div className="p-2 bg-indigo-50 rounded-lg text-indigo-600 group-hover:bg-indigo-100 transition-colors">
                  {icon}
                </div>
                <span className="font-medium text-gray-800">{label}</span>
              </div>
              <p className="text-sm text-gray-600">{description}</p>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}