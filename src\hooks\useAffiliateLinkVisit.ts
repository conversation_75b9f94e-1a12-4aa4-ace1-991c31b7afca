import { useMutation, useQuery } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import {
  visitAffiliateLink,
  visitAffiliateLinkWithData,
  extractUtmParams,
  saveVisitDataToStorage,
  getVisitDataFromStorage,
  LinkVisitResponse
} from '../services/affiliateLinks';

/**
 * Hook para gerenciar visitas a links de afiliados
 * @returns Objeto com funções e estados para gerenciar visitas
 */
export const useAffiliateLinkVisit = () => {
  // Mutation para registrar visita simples
  const visitLinkMutation = useMutation({
    mutationFn: (linkId: string) => visitAffiliateLink(linkId),
    onSuccess: (data, linkId) => {
      // Salvar dados da visita no localStorage
      saveVisitDataToStorage(data.data, linkId);
      
      console.log('Visita registrada com sucesso:', data);
    },
    onError: (error) => {
      console.error('Erro ao registrar visita:', error);
      // Não mostrar toast de erro para não atrapalhar a experiência do usuário
    }
  });

  // Mutation para registrar visita com dados adicionais
  const visitLinkWithDataMutation = useMutation({
    mutationFn: ({ 
      linkId, 
      additionalData 
    }: { 
      linkId: string; 
      additionalData?: Parameters<typeof visitAffiliateLinkWithData>[1] 
    }) => visitAffiliateLinkWithData(linkId, additionalData),
    onSuccess: (data, { linkId }) => {
      // Salvar dados da visita no localStorage
      saveVisitDataToStorage(data.data, linkId);
      
      console.log('Visita com dados registrada com sucesso:', data);
    },
    onError: (error) => {
      console.error('Erro ao registrar visita com dados:', error);
      // Não mostrar toast de erro para não atrapalhar a experiência do usuário
    }
  });

  /**
   * Registra uma visita simples no link
   * @param linkId ID do link de afiliado
   */
  const visitLink = (linkId: string) => {
    visitLinkMutation.mutate(linkId);
  };

  /**
   * Registra uma visita no link com dados UTM e referrer
   * @param linkId ID do link de afiliado
   * @param customData Dados customizados adicionais
   */
  const visitLinkWithTracking = (
    linkId: string, 
    customData?: Parameters<typeof visitAffiliateLinkWithData>[1]
  ) => {
    // Extrair parâmetros UTM da URL atual
    const utmParams = extractUtmParams();
    
    // Combinar dados UTM com dados customizados
    const additionalData = {
      ...utmParams,
      ...customData
    };

    visitLinkWithDataMutation.mutate({ linkId, additionalData });
  };

  /**
   * Registra uma visita automaticamente baseada nos parâmetros da URL
   * @param linkId ID do link de afiliado
   */
  const autoVisitLink = (linkId: string) => {
    // Verificar se já visitou este link recentemente (últimos 5 minutos)
    const visitData = getVisitDataFromStorage();
    const now = new Date().getTime();
    const fiveMinutesAgo = now - (5 * 60 * 1000);

    if (visitData?.lastVisit?.linkId === linkId) {
      const lastVisitTime = new Date(visitData.lastVisit.visitedAt).getTime();
      if (lastVisitTime > fiveMinutesAgo) {
        console.log('Visita recente detectada, pulando registro');
        return;
      }
    }

    // Registrar visita com tracking completo
    visitLinkWithTracking(linkId);
  };

  /**
   * Verifica se um link foi visitado recentemente
   * @param linkId ID do link de afiliado
   * @param timeWindowMinutes Janela de tempo em minutos (padrão: 5)
   * @returns true se foi visitado recentemente
   */
  const wasRecentlyVisited = (linkId: string, timeWindowMinutes: number = 5): boolean => {
    const visitData = getVisitDataFromStorage();
    if (!visitData?.lastVisit || visitData.lastVisit.linkId !== linkId) {
      return false;
    }

    const now = new Date().getTime();
    const timeWindow = now - (timeWindowMinutes * 60 * 1000);
    const lastVisitTime = new Date(visitData.lastVisit.visitedAt).getTime();

    return lastVisitTime > timeWindow;
  };

  /**
   * Obtém dados de visitas do localStorage
   * @returns Dados de visitas armazenados
   */
  const getStoredVisitData = () => {
    return getVisitDataFromStorage();
  };

  /**
   * Query para obter dados de visitas (se necessário para debugging)
   */
  const { data: storedVisits } = useQuery({
    queryKey: ['affiliateVisits'],
    queryFn: getVisitDataFromStorage,
    staleTime: Infinity, // Dados locais não ficam stale
    refetchOnWindowFocus: false,
  });

  return {
    // Funções principais
    visitLink,
    visitLinkWithTracking,
    autoVisitLink,
    
    // Funções utilitárias
    wasRecentlyVisited,
    getStoredVisitData,
    extractUtmParams,
    
    // Estados
    isVisiting: visitLinkMutation.isPending || visitLinkWithDataMutation.isPending,
    visitError: visitLinkMutation.error || visitLinkWithDataMutation.error,
    lastVisitData: visitLinkMutation.data || visitLinkWithDataMutation.data,
    storedVisits,
    
    // Dados das mutations
    visitMutation: visitLinkMutation,
    visitWithDataMutation: visitLinkWithDataMutation
  };
};
