import { useMutation } from '@tanstack/react-query';
import { visitAffiliateLink } from '../services/affiliateLinks';

/**
 * Hook simples para registrar visitas a links de afiliados
 */
export const useAffiliateLinkVisit = () => {
  const visitMutation = useMutation({
    mutationFn: visitAffiliateLink,
    onError: (error) => {
      console.error('Erro ao registrar visita:', error);
    }
  });

  return {
    visitLink: visitMutation.mutate,
    isVisiting: visitMutation.isPending,
    error: visitMutation.error
  };
};
