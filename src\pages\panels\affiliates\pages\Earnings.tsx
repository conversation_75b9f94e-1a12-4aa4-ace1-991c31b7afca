import { FiDollarSign, FiRefresh<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>heck } from 'react-icons/fi';
import { useAffiliateDashboard } from '../../../../hooks/useAffiliateDashboard';
import { CommissionByStatus } from '../../../../services/affiliateDashboard';
import Message from 'rsuite/Message';
import 'rsuite/Message/styles/index.css';

const Earnings = () => {
  // Hook para buscar dados do dashboard
  const {
    totalEarnings,
    pendingEarnings,
    lastPaymentValue,
    lastPaymentDate,
    commissionsByStatus,
    isLoading,
    error,
    refetch,
    isRefetching
  } = useAffiliateDashboard();

  const brlFormat = (value: number) => {
    return new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(value)
  }

  // Função para formatar data
  const formatDate = (dateString?: string) => {
    if (!dateString) return '__/__/____';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Meus Ganhos</h2>
        <button
          onClick={() => refetch()}
          disabled={isRefetching}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <FiRefreshCw className={`h-4 w-4 ${isRefetching ? 'animate-spin' : ''}`} />
          {isRefetching ? 'Atualizando...' : 'Atualizar'}
        </button>
      </div>

      {/* Erro */}
      {error && (
        <div className="mb-6">
          <Message showIcon type="error" header="Erro ao carregar dados">
            <div className="mb-4">Não foi possível carregar os dados de ganhos. Tente novamente.</div>
            <button
              onClick={() => refetch()}
              disabled={isRefetching}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {isRefetching ? 'Atualizando...' : 'Tentar Novamente'}
            </button>
          </Message>
        </div>
      )}

      {/* Cards de resumo */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 mb-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                <FiDollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Ganhos Totais</dt>
                  <dd className="text-3xl font-semibold text-gray-900">
                    {isLoading ? (
                      <div className="animate-pulse bg-gray-200 h-8 w-24 rounded"></div>
                    ) : (
                      brlFormat(totalEarnings)
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-blue-500 rounded-md p-3">
                <FiClock className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Pendente</dt>
                  <dd className="text-3xl font-semibold text-gray-900">
                    {isLoading ? (
                      <div className="animate-pulse bg-gray-200 h-8 w-24 rounded"></div>
                    ) : (
                      brlFormat(pendingEarnings)
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-purple-500 rounded-md p-3">
                <FiCheck className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Último Pagamento</dt>
                  <dd className="text-3xl font-semibold text-gray-900">
                    {isLoading ? (
                      <div className="animate-pulse bg-gray-200 h-8 w-24 rounded"></div>
                    ) : (
                      brlFormat(lastPaymentValue)
                    )}
                  </dd>
                  <dd className="text-sm text-gray-500">
                    {isLoading ? (
                      <div className="animate-pulse bg-gray-200 h-4 w-20 rounded mt-1"></div>
                    ) : (
                      formatDate(lastPaymentDate)
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Comissões por Status */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Comissões por Status</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Resumo das suas comissões organizadas por status</p>
        </div>
        <div className="border-t border-gray-200">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantidade
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Valor Total
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {isLoading ? (
                  <tr>
                    <td colSpan={3} className="px-6 py-12 text-center">
                      <div className="animate-pulse">
                        <div className="bg-gray-200 h-4 w-32 rounded mx-auto mb-2"></div>
                        <div className="bg-gray-200 h-4 w-24 rounded mx-auto"></div>
                      </div>
                    </td>
                  </tr>
                ) : commissionsByStatus.length === 0 ? (
                  <tr>
                    <td colSpan={3} className="px-6 py-12 text-center text-gray-500">
                      Nenhuma comissão encontrada
                    </td>
                  </tr>
                ) : (
                  commissionsByStatus.map((commission: CommissionByStatus, index: number) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          commission.status === 'paid'
                            ? 'bg-green-100 text-green-800'
                            : commission.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : commission.status === 'processing'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {commission.status === 'paid' ? 'Pago' :
                           commission.status === 'pending' ? 'Pendente' :
                           commission.status === 'processing' ? 'Processando' :
                           commission.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{commission.count}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{brlFormat(commission.total_value)}</div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Earnings
