import React from 'react';
import { Share2, Camera, Download, X } from 'lucide-react';
import type { Progress } from '../types';

interface ShareProgressProps {
  progress: Progress;
  weeklyAdherence: number;
  streak: number;
}

export function ShareProgress({ progress, weeklyAdherence, streak }: ShareProgressProps) {
  const [showModal, setShowModal] = React.useState(false);
  const [shareType, setShareType] = React.useState<'stats' | 'photo' | null>(null);
  
  const handleShare = async () => {
    if (!shareType) return;
    
    try {
      const data = {
        title: 'Meu Progresso Fitness',
        text: `🏋️‍♂️ ${streak} dias de streak!\n💪 ${weeklyAdherence}% de aderência essa semana\n⚖️ Progresso: ${progress.weight}kg`,
        url: window.location.href
      };
      
      if (navigator.share) {
        await navigator.share(data);
      }
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  return (
    <>
      <button
        onClick={() => setShowModal(true)}
        className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
      >
        <Share2 className="w-4 h-4" />
        <span>Compartilhar Progresso</span>
      </button>

      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-800">
                Compartilhar Progresso
              </h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-6">
              <button
                onClick={() => setShareType('stats')}
                className={`flex flex-col items-center gap-2 p-4 rounded-lg border-2 transition-colors ${
                  shareType === 'stats'
                    ? 'border-indigo-500 bg-indigo-50'
                    : 'border-gray-200 hover:border-indigo-500 hover:bg-indigo-50'
                }`}
              >
                <Download className="w-6 h-6 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">Estatísticas</span>
              </button>
              <button
                onClick={() => setShareType('photo')}
                className={`flex flex-col items-center gap-2 p-4 rounded-lg border-2 transition-colors ${
                  shareType === 'photo'
                    ? 'border-indigo-500 bg-indigo-50'
                    : 'border-gray-200 hover:border-indigo-500 hover:bg-indigo-50'
                }`}
              >
                <Camera className="w-6 h-6 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">Foto</span>
              </button>
            </div>

            {shareType === 'stats' && (
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Streak Atual:</span>
                    <span className="font-medium text-gray-800">{streak} dias</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Aderência Semanal:</span>
                    <span className="font-medium text-gray-800">{weeklyAdherence}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Peso Atual:</span>
                    <span className="font-medium text-gray-800">{progress.weight}kg</span>
                  </div>
                </div>
              </div>
            )}

            {shareType === 'photo' && (
              <div className="aspect-[4/5] bg-gray-100 rounded-lg mb-6 flex items-center justify-center">
                <Camera className="w-8 h-8 text-gray-400" />
              </div>
            )}

            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleShare}
                disabled={!shareType}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Compartilhar
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}