import { apiService } from './api';

export interface AdminSubscription {
  id: string;
  user_name: string;
  user_email: string;
  status: string;
  platform: string;
  price: number;
  start_date: string;
  end_date: string;
  next_billing_date: string;
  snaptokens: number;
  created_at: string;
  plan_name: string;
  payment_provider_name: string;
}

export interface AdminSubscriptionsResponse {
  status: string;
  data: AdminSubscription[];
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

export interface SubscriptionFilters {
  page?: number;
  limit?: number;
  status?: string;
  platform?: string;
  search?: string;
  start_date?: string;
  end_date?: string;
}

/**
 * Busca as assinaturas administrativas
 * @param filters Filtros para a busca
 * @returns Lista de assinaturas com paginação
 */
export const getAdminSubscriptions = async (
  filters: SubscriptionFilters = {}
): Promise<AdminSubscriptionsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    // Adicionar parâmetros de paginação
    queryParams.append('page', (filters.page || 1).toString());
    queryParams.append('limit', (filters.limit || 10).toString());

    // Adicionar filtros opcionais
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.platform) queryParams.append('platform', filters.platform);
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.start_date) queryParams.append('start_date', filters.start_date);
    if (filters.end_date) queryParams.append('end_date', filters.end_date);

    const response = await apiService.get<AdminSubscriptionsResponse>(
      `admin/subscriptions?${queryParams.toString()}`
    );

    if (response.status === 'success') {
      return response;
    }

    throw new Error('Erro ao buscar assinaturas');
  } catch (error) {
    console.error('Erro ao buscar assinaturas administrativas:', error);
    throw error;
  }
};

/**
 * Cancela uma assinatura
 * @param subscriptionId ID da assinatura
 * @param immediate Se deve cancelar imediatamente
 * @returns Resposta da API
 */
export const cancelAdminSubscription = async (
  subscriptionId: string,
  immediate: boolean = false
): Promise<any> => {
  try {
    const response = await apiService.post(
      `admin/subscriptions/${subscriptionId}/cancel`,
      { immediate }
    );

    return response;
  } catch (error) {
    console.error('Erro ao cancelar assinatura:', error);
    throw error;
  }
};

/**
 * Atualiza o status de uma assinatura
 * @param subscriptionId ID da assinatura
 * @param status Novo status
 * @returns Resposta da API
 */
export const updateSubscriptionStatus = async (
  subscriptionId: string,
  status: string
): Promise<any> => {
  try {
    const response = await apiService.put(
      `admin/subscriptions/${subscriptionId}/status`,
      { status }
    );

    return response;
  } catch (error) {
    console.error('Erro ao atualizar status da assinatura:', error);
    throw error;
  }
};
