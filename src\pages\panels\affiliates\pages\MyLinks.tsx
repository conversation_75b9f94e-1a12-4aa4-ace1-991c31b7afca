import { Trash<PERSON>con, Loader2 } from 'lucide-react'
import { FiLink, FiCopy, FiBarChart2 } from 'react-icons/fi'
import { useState, useEffect } from 'react'
import { apiService } from '../../../../services/api'
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query'
import { toast } from 'react-toastify'

// Interface para os links de afiliados
interface AffiliateLink {
  id: number
  name: string
  invite: string
  link_type: string
  created_at: string
  updated_at: string
  clicks?: number
  conversions?: number
  earnings?: number
}

// Interface para a resposta da API
interface ApiResponse<T> {
  status: string
  data: T
}

const MyLinks = () => {
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [formData, setFormData] = useState({
    invite: generateRandomSlug(8),
    link_type: 'signup_user',
    name: ''
  })
  const [isMaster, setIsMaster] = useState(false)
  const queryClient = useQueryClient()

  useEffect(() => {
    // Check if user is master affiliate
    const affiliateData = localStorage.getItem('snapfit_affiliates')
    if (affiliateData) {
      const parsedData = JSON.parse(affiliateData)
      setIsMaster(parsedData.is_master === true)
    }
  }, [])

  // Generate random slug
  function generateRandomSlug(length: number) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  const createLinkMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      const response = await apiService.post<ApiResponse<AffiliateLink>>('affiliates/links', data)
      return response
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['affiliateLinks'] })
      setShowCreateForm(false)
      toast.success('Link criado com sucesso!', { position: 'bottom-right' })
      setFormData({
        invite: generateRandomSlug(8),
        link_type: 'signup_user',
        name: ''
      })
    },
    onError: (error: any) => {
      console.error('Erro ao criar link:', error)
      toast.error('Erro ao criar link. Tente novamente.', { position: 'bottom-right' })
    }
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    createLinkMutation.mutate(formData)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target

    if (name === 'invite') {
      // Convert to lowercase and remove spaces
      const formattedValue = value.toLowerCase().replace(/\s+/g, '')
      setFormData(prev => ({ ...prev, [name]: formattedValue }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Link copiado para a área de transferência!', { position: 'bottom-right' })
  }

  // Buscar links de afiliado usando React Query
  const {
    data: affiliateLinksResponse,
    isLoading,
    isError,
    error
  } = useQuery<ApiResponse<AffiliateLink[]>>({
    queryKey: ['affiliateLinks'],
    queryFn: async () => {
      return await apiService.get('affiliates/links')
    }
  })

  // Extrair os links da resposta da API
  const affiliateLinks = affiliateLinksResponse?.data || []

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Meus Links de Afiliado</h2>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center"
        >
          <FiLink className="mr-2" />
          Criar Novo Link
        </button>
      </div>

      {/* Form para criar novo link */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6">
            <h3 className="text-xl font-bold mb-4">Criar Novo Link de Afiliado</h3>
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nome
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Nome para identificar este link
                </p>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cupom
                </label>
                <input
                  type="text"
                  name="invite"
                  value={formData.invite}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Este será o código usado no seu link de convite
                </p>
              </div>

              {isMaster && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tipo de Link
                  </label>
                  <select
                    name="link_type"
                    value={formData.link_type}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="signup_user">Cadastro de Usuário</option>
                    <option value="signup_affiliate">Cadastro de Afiliado</option>
                  </select>
                </div>
              )}

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={createLinkMutation.isPending}
                >
                  {createLinkMutation.isPending ? 'Criando...' : 'Criar Link'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Tabela de links */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {isLoading ? (
          <div className="flex justify-center items-center p-8">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Carregando links...</span>
          </div>
        ) : isError ? (
          <div className="p-8 text-center text-red-500">
            <p>Erro ao carregar os links. Por favor, tente novamente.</p>
            <p className="text-sm mt-2">{error instanceof Error ? error.message : 'Erro desconhecido'}</p>
          </div>
        ) : affiliateLinks.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <p>Você ainda não tem links de afiliado. Crie seu primeiro link para começar a promover.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nome
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cupom
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Link
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tipo
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Criado em
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {affiliateLinks.map((link) => {
                  const inviteUrl = `${window.location.origin}/invite?ref=${link.invite}`
                  const linkType = link.link_type === 'signup_user' ? 'Usuário' : 'Afiliado'
                  const formattedDate = new Date(link.created_at).toLocaleDateString('pt-BR')

                  return (
                    <tr key={link.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{link.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{link.invite}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500 max-w-xs truncate">{inviteUrl}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{linkType}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{formattedDate}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => copyToClipboard(inviteUrl)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Copiar link"
                          >
                            <FiCopy />
                          </button>
                          <button
                            className="hidden text-blue-600 hover:text-blue-900"
                            title="Ver estatísticas"
                          >
                            <FiBarChart2 />
                          </button>
                          <button
                            className="text-red-600 hover:text-red-900"
                            title="Excluir link"
                          >
                            <TrashIcon size={15} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )
}

export default MyLinks

