import { apiService } from './api';

export interface Plan {
  name: string;
  description: string;
  price: number;
  snaptokens: number;
  selected: boolean;
  config_id: string;
}

export interface PlansResponse {
  status: string;
  data: Plan[];
}

export interface CheckoutResponse {
  status: string;
  data: {
    url: string;
  };
}

/**
 * Busca os planos disponíveis para o usuário
 * @returns Lista de planos disponíveis
 */
export const getUserPlans = async (): Promise<Plan[]> => {
  try {
    const response = await apiService.get<PlansResponse>('users/plans');
    
    if (response.status === 'success' && response.data) {
      return response.data;
    }
    
    throw new Error('Erro ao buscar planos');
  } catch (error) {
    console.error('Erro ao buscar planos do usuário:', error);
    throw error;
  }
};

/**
 * Inicia o processo de checkout para um plano específico
 * @param configId ID da configuração do plano
 * @returns URL de redirecionamento para o Stripe
 */
export const createPlanCheckout = async (configId: string): Promise<string> => {
  try {
    const response = await apiService.post<CheckoutResponse>(`users/plans/checkout/${configId}`, {});
    
    if (response.status === 'success' && response.data?.url) {
      return response.data.url;
    }
    
    throw new Error('Erro ao criar checkout');
  } catch (error) {
    console.error('Erro ao criar checkout do plano:', error);
    throw error;
  }
};
