import { apiService } from './api';

// Cache simples para evitar múltiplas requisições na mesma sessão
const visitedLinks = new Set<string>();

/**
 * Registra uma visita no link de afiliado (apenas uma vez por sessão)
 * @param linkId ID do link de afiliado
 */
export const visitAffiliateLink = async (linkId: string): Promise<void> => {
  // Se já foi visitado nesta sessão, não faz nada
  if (visitedLinks.has(linkId)) {
    console.log(`Link ${linkId} já foi visitado nesta sessão`);
    return;
  }

  try {
    // Marcar como visitado imediatamente para evitar duplicatas
    visitedLinks.add(linkId);

    // Fazer a requisição
    await apiService.get(`affiliates/links/visit/${linkId}`);
    console.log(`Visita registrada para link: ${linkId}`);
  } catch (error) {
    console.error('Erro ao registrar visita:', error);
    // Remover do cache se deu erro para permitir nova tentativa
    visitedLinks.delete(linkId);
  }
};
