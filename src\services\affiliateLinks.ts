import { apiService } from './api';

export interface LinkVisitResponse {
  status: string;
  data: {
    id: string;
    affiliate_id: string;
    link_id: string;
    ip_address: string;
    user_agent: string;
    referrer?: string;
    visited_at: string;
    converted: boolean;
    conversion_value?: number;
  };
}

export interface LinkVisitError {
  status: string;
  message: string;
  code?: string;
}

/**
 * Registra uma visita no link de afiliado
 * @param linkId ID do link de afiliado
 * @returns Dados da visita registrada
 */
export const visitAffiliateLink = async (linkId: string): Promise<LinkVisitResponse> => {
  try {
    // Verificar se já foi visitado recentemente
    if (wasRecentlyVisited(linkId)) {
      console.log(`Link ${linkId} já foi visitado recentemente, pulando requisição`);
      // Retornar dados do localStorage se disponível
      const storedData = getVisitDataFromStorage();
      if (storedData?.lastVisit?.linkId === linkId) {
        return {
          status: 'success',
          data: {
            id: storedData.lastVisit.visitId,
            affiliate_id: storedData.lastVisit.affiliateId,
            link_id: linkId,
            ip_address: '',
            user_agent: navigator.userAgent,
            visited_at: storedData.lastVisit.visitedAt,
            converted: storedData.lastVisit.converted || false
          }
        };
      }
    }

    // Verificar se já existe uma requisição em andamento para este link
    if (pendingVisits.has(linkId)) {
      console.log(`Requisição já em andamento para link ${linkId}, aguardando...`);
      return await pendingVisits.get(linkId)!;
    }

    // Criar nova requisição
    const visitPromise = apiService.get<LinkVisitResponse>(
      `affiliates/links/visit/${linkId}`
    );

    // Armazenar a promise no cache
    pendingVisits.set(linkId, visitPromise);

    try {
      const response = await visitPromise;

      if (response.status === 'success') {
        // Salvar no localStorage
        saveVisitDataToStorage(response.data, linkId);
        return response;
      }

      throw new Error('Erro ao registrar visita no link');
    } finally {
      // Remover do cache após completar
      pendingVisits.delete(linkId);
    }
  } catch (error) {
    console.error('Erro ao visitar link de afiliado:', error);
    // Remover do cache em caso de erro
    pendingVisits.delete(linkId);
    throw error;
  }
};

/**
 * Registra uma visita no link de afiliado com dados adicionais
 * @param linkId ID do link de afiliado
 * @param additionalData Dados adicionais da visita
 * @returns Dados da visita registrada
 */
export const visitAffiliateLinkWithData = async (
  linkId: string,
  additionalData?: {
    referrer?: string;
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    utm_content?: string;
    utm_term?: string;
  }
): Promise<LinkVisitResponse> => {
  try {
    // Verificar se já foi visitado recentemente
    if (wasRecentlyVisited(linkId)) {
      console.log(`Link ${linkId} já foi visitado recentemente, pulando requisição`);
      // Retornar dados do localStorage se disponível
      const storedData = getVisitDataFromStorage();
      if (storedData?.lastVisit?.linkId === linkId) {
        return {
          status: 'success',
          data: {
            id: storedData.lastVisit.visitId,
            affiliate_id: storedData.lastVisit.affiliateId,
            link_id: linkId,
            ip_address: '',
            user_agent: navigator.userAgent,
            visited_at: storedData.lastVisit.visitedAt,
            converted: storedData.lastVisit.converted || false
          }
        };
      }
    }

    // Criar chave única para o cache incluindo os parâmetros
    const cacheKey = `${linkId}_${JSON.stringify(additionalData || {})}`;

    // Verificar se já existe uma requisição em andamento
    if (pendingVisits.has(cacheKey)) {
      console.log(`Requisição já em andamento para link ${linkId} com dados, aguardando...`);
      return await pendingVisits.get(cacheKey)!;
    }

    const queryParams = new URLSearchParams();

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value);
        }
      });
    }

    const url = queryParams.toString()
      ? `affiliates/links/visit/${linkId}?${queryParams.toString()}`
      : `affiliates/links/visit/${linkId}`;

    // Criar nova requisição
    const visitPromise = apiService.get<LinkVisitResponse>(url);

    // Armazenar a promise no cache
    pendingVisits.set(cacheKey, visitPromise);

    try {
      const response = await visitPromise;

      if (response.status === 'success') {
        // Salvar no localStorage
        saveVisitDataToStorage(response.data, linkId);
        return response;
      }

      throw new Error('Erro ao registrar visita no link');
    } finally {
      // Remover do cache após completar
      pendingVisits.delete(cacheKey);
    }
  } catch (error) {
    console.error('Erro ao visitar link de afiliado com dados:', error);
    // Remover do cache em caso de erro
    const cacheKey = `${linkId}_${JSON.stringify(additionalData || {})}`;
    pendingVisits.delete(cacheKey);
    throw error;
  }
};

/**
 * Extrai parâmetros UTM da URL atual
 * @returns Objeto com parâmetros UTM encontrados
 */
export const extractUtmParams = () => {
  const urlParams = new URLSearchParams(window.location.search);

  return {
    utm_source: urlParams.get('utm_source') || undefined,
    utm_medium: urlParams.get('utm_medium') || undefined,
    utm_campaign: urlParams.get('utm_campaign') || undefined,
    utm_content: urlParams.get('utm_content') || undefined,
    utm_term: urlParams.get('utm_term') || undefined,
    referrer: document.referrer || undefined
  };
};

// Cache para controlar requisições em andamento
const pendingVisits = new Map<string, Promise<LinkVisitResponse>>();

/**
 * Verifica se uma visita foi registrada recentemente
 * @param linkId ID do link
 * @param timeWindowMinutes Janela de tempo em minutos
 * @returns true se foi visitado recentemente
 */
export const wasRecentlyVisited = (linkId: string, timeWindowMinutes: number = 5): boolean => {
  try {
    const storageKey = 'snapfit_affiliate_visit';
    const data = localStorage.getItem(storageKey);
    if (!data) return false;

    const visitData = JSON.parse(data);
    if (!visitData.lastVisit || visitData.lastVisit.linkId !== linkId) {
      return false;
    }

    const now = new Date().getTime();
    const timeWindow = now - (timeWindowMinutes * 60 * 1000);
    const lastVisitTime = new Date(visitData.lastVisit.visitedAt).getTime();

    return lastVisitTime > timeWindow;
  } catch (error) {
    console.error('Erro ao verificar visita recente:', error);
    return false;
  }
};

/**
 * Salva dados da visita no localStorage para tracking
 * @param visitData Dados da visita
 * @param linkId ID do link visitado
 */
export const saveVisitDataToStorage = (visitData: LinkVisitResponse['data'], linkId: string) => {
  try {
    const storageKey = 'snapfit_affiliate_visit';
    const existingData = localStorage.getItem(storageKey);
    const data = existingData ? JSON.parse(existingData) : {};

    localStorage.setItem(storageKey, JSON.stringify({
      ...data,
      lastVisit: {
        linkId,
        visitId: visitData.id,
        affiliateId: visitData.affiliate_id,
        visitedAt: visitData.visited_at,
        converted: visitData.converted
      },
      visits: [
        ...(data.visits || []),
        {
          linkId,
          visitId: visitData.id,
          affiliateId: visitData.affiliate_id,
          visitedAt: visitData.visited_at
        }
      ].slice(-10) // Manter apenas as últimas 10 visitas
    }));
  } catch (error) {
    console.error('Erro ao salvar dados da visita no localStorage:', error);
  }
};

/**
 * Recupera dados de visitas do localStorage
 * @returns Dados de visitas armazenados
 */
export const getVisitDataFromStorage = () => {
  try {
    const storageKey = 'snapfit_affiliate_visit';
    const data = localStorage.getItem(storageKey);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Erro ao recuperar dados de visita do localStorage:', error);
    return null;
  }
};

/**
 * Limpa dados de visitas do localStorage
 */
export const clearVisitDataFromStorage = () => {
  try {
    localStorage.removeItem('snapfit_affiliate_visit');
  } catch (error) {
    console.error('Erro ao limpar dados de visita do localStorage:', error);
  }
};

/**
 * Limpa o cache de requisições pendentes
 * Útil para debugging ou quando necessário forçar novas requisições
 */
export const clearPendingVisitsCache = () => {
  pendingVisits.clear();
  console.log('Cache de visitas pendentes limpo');
};

/**
 * Obtém informações sobre requisições pendentes
 * Útil para debugging
 */
export const getPendingVisitsInfo = () => {
  return {
    count: pendingVisits.size,
    keys: Array.from(pendingVisits.keys())
  };
};
