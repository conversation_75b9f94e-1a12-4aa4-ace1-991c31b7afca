import { apiService } from './api';

export interface LinkVisitResponse {
  status: string;
  data: {
    id: string;
    affiliate_id: string;
    link_id: string;
    ip_address: string;
    user_agent: string;
    referrer?: string;
    visited_at: string;
    converted: boolean;
    conversion_value?: number;
  };
}

export interface LinkVisitError {
  status: string;
  message: string;
  code?: string;
}

/**
 * Registra uma visita no link de afiliado
 * @param linkId ID do link de afiliado
 * @returns Dados da visita registrada
 */
export const visitAffiliateLink = async (linkId: string): Promise<LinkVisitResponse> => {
  try {
    const response = await apiService.get<LinkVisitResponse>(
      `affiliates/links/visit/${linkId}`
    );
    
    if (response.status === 'success') {
      return response;
    }
    
    throw new Error('Erro ao registrar visita no link');
  } catch (error) {
    console.error('Erro ao visitar link de afiliado:', error);
    throw error;
  }
};

/**
 * Registra uma visita no link de afiliado com dados adicionais
 * @param linkId ID do link de afiliado
 * @param additionalData Dados adicionais da visita
 * @returns Dados da visita registrada
 */
export const visitAffiliateLinkWithData = async (
  linkId: string,
  additionalData?: {
    referrer?: string;
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    utm_content?: string;
    utm_term?: string;
  }
): Promise<LinkVisitResponse> => {
  try {
    const queryParams = new URLSearchParams();
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value);
        }
      });
    }
    
    const url = queryParams.toString() 
      ? `affiliates/links/visit/${linkId}?${queryParams.toString()}`
      : `affiliates/links/visit/${linkId}`;
    
    const response = await apiService.get<LinkVisitResponse>(url);
    
    if (response.status === 'success') {
      return response;
    }
    
    throw new Error('Erro ao registrar visita no link');
  } catch (error) {
    console.error('Erro ao visitar link de afiliado com dados:', error);
    throw error;
  }
};

/**
 * Extrai parâmetros UTM da URL atual
 * @returns Objeto com parâmetros UTM encontrados
 */
export const extractUtmParams = () => {
  const urlParams = new URLSearchParams(window.location.search);
  
  return {
    utm_source: urlParams.get('utm_source') || undefined,
    utm_medium: urlParams.get('utm_medium') || undefined,
    utm_campaign: urlParams.get('utm_campaign') || undefined,
    utm_content: urlParams.get('utm_content') || undefined,
    utm_term: urlParams.get('utm_term') || undefined,
    referrer: document.referrer || undefined
  };
};

/**
 * Salva dados da visita no localStorage para tracking
 * @param visitData Dados da visita
 * @param linkId ID do link visitado
 */
export const saveVisitDataToStorage = (visitData: LinkVisitResponse['data'], linkId: string) => {
  try {
    const storageKey = 'snapfit_affiliate_visit';
    const existingData = localStorage.getItem(storageKey);
    const data = existingData ? JSON.parse(existingData) : {};
    
    localStorage.setItem(storageKey, JSON.stringify({
      ...data,
      lastVisit: {
        linkId,
        visitId: visitData.id,
        affiliateId: visitData.affiliate_id,
        visitedAt: visitData.visited_at,
        converted: visitData.converted
      },
      visits: [
        ...(data.visits || []),
        {
          linkId,
          visitId: visitData.id,
          affiliateId: visitData.affiliate_id,
          visitedAt: visitData.visited_at
        }
      ].slice(-10) // Manter apenas as últimas 10 visitas
    }));
  } catch (error) {
    console.error('Erro ao salvar dados da visita no localStorage:', error);
  }
};

/**
 * Recupera dados de visitas do localStorage
 * @returns Dados de visitas armazenados
 */
export const getVisitDataFromStorage = () => {
  try {
    const storageKey = 'snapfit_affiliate_visit';
    const data = localStorage.getItem(storageKey);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Erro ao recuperar dados de visita do localStorage:', error);
    return null;
  }
};

/**
 * Limpa dados de visitas do localStorage
 */
export const clearVisitDataFromStorage = () => {
  try {
    localStorage.removeItem('snapfit_affiliate_visit');
  } catch (error) {
    console.error('Erro ao limpar dados de visita do localStorage:', error);
  }
};
