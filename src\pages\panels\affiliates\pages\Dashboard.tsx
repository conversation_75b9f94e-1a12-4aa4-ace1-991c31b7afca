import { <PERSON>Link, <PERSON><PERSON><PERSON><PERSON><PERSON>ign, <PERSON><PERSON><PERSON>, FiBarChart2 } from 'react-icons/fi';
import Message from 'rsuite/Message';
import 'rsuite/Message/styles/index.css';
import { useNavigate } from 'react-router-dom';
import { useAffiliateOnboarding } from '../../../../hooks/useAffiliateOnboarding';
import { apiService } from '../../../../services/api';
import LoadingOverlay from '../../../../components/LoadingOverlay';
import { useState } from 'react';

const Dashboard = () => {
  const [isLoadingPage, setIsLoadingPage] = useState(false);
  const navigate = useNavigate();

  // Usar o hook personalizado para gerenciar o status de onboarding
  const { onboardingStatus, isLoading, error } = useAffiliateOnboarding();

  const postOnboarding = async () => {
    try {
      setIsLoadingPage(true);
      const response: any = await apiService.post('affiliates/onboarding', { onboarding: true });
      return response.data;
    } catch (error) {
      console.error('Erro ao marcar onboarding como completo:', error);
      return null;
    } finally {
      setIsLoadingPage(false);
    }
  }

  // Função para navegar para a página de completar o cadastro
  const handleCompleteOnboarding = async () => {
    const result = await postOnboarding();
    if (result) {
      // remove onboarding from localStorage of snapfit_affiliates block
      const storedData = localStorage.getItem('snapfit_affiliates');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        if (parsedData.onboarding) delete parsedData.onboarding;
        localStorage.setItem('snapfit_affiliates', JSON.stringify(parsedData));
      }
      window.location.href = result.accountLink;
    }    
  };

  return (
    <>
    {isLoadingPage && (
      <LoadingOverlay />
    )}

    <div className="max-w-7xl mx-auto">

    {/* Mensagens de status de onboarding */}
    <div className="mb-4">
      {isLoading ? (
        <div className="flex justify-center items-center p-4 bg-gray-100 rounded-lg">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-3"></div>
          <span>Verificando status da sua conta...</span>
        </div>
      ) : error ? (
        <Message showIcon type="error" header="Erro ao verificar status da conta">
          <div className="mb-4">Não foi possível verificar o status da sua conta. Tente novamente mais tarde.</div>
        </Message>
      ) : onboardingStatus === 'onboarding' ? (
        <Message showIcon type="warning" header="Você precisa completar a sua conta">
          <div className="mb-4">Para receber as suas comissões é importante que você complete os dados da sua conta.</div>
          <button
            onClick={handleCompleteOnboarding}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Completar agora
          </button>
        </Message>
      ) : onboardingStatus === 'pending' ? (
        <Message showIcon type="warning" header="A sua conta está em análise">
          <div className="mb-4">Suas informações estão sendo verificadas.</div>
        </Message>
      ) : null}
    </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 mb-8">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-blue-500 rounded-md p-3">
                <FiLink className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total de Cliques</dt>
                  <dd className="text-3xl font-semibold text-gray-900">0</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <a href="#" className="font-medium text-blue-600 hover:text-blue-500 flex items-center">
                <FiBarChart2 className="mr-1" /> Ver detalhes
              </a>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                <FiDollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Ganhos Totais</dt>
                  <dd className="text-3xl font-semibold text-gray-900">R$ 0,00</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <a href="#" className="font-medium text-blue-600 hover:text-blue-500 flex items-center">
                <FiBarChart2 className="mr-1" /> Ver detalhes
              </a>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-purple-500 rounded-md p-3">
                <FiUser className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Conversões</dt>
                  <dd className="text-3xl font-semibold text-gray-900">0</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <a href="#" className="font-medium text-blue-600 hover:text-blue-500 flex items-center">
                <FiBarChart2 className="mr-1" /> Ver detalhes
              </a>
            </div>
          </div>
        </div>
      </div>

    </div>
    
    </>
  )
}

export default Dashboard
