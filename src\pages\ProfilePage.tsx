import React, { useEffect, useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Medal, Award, Trophy, Users, Settings, SparkleIcon } from 'lucide-react';
import { ProfessionalCard } from '../components/ProfessionalCard';
// import { PointsHistory } from '../components/PointsHistory';
import { FriendsRanking } from '../components/FriendsRanking';
import { PlansModal } from '../components/PlansModal';
import { mockProfessionals } from '../data/mockProfessionals';
import { getUserPoints } from '../services/points';
import type { PointsHistory as PointsHistoryType } from '../types/points';
import img_user_profile from '../assets/images/user-profile.png';
import axiosInstance from '../axiosInstance';
import LoadingOverlay from '../components/LoadingOverlay';
import { BadgeComingSoon } from '../components/BadgetComingSoon';

const achievements = [
  {
    icon: Medal,
    color: 'text-yellow-500',
    title: '7 Dias Seguidos',
    description: 'Completou todas as refeições por 7 dias'
  },
  {
    icon: Award,
    color: 'text-blue-500',
    title: 'Mestre do Treino',
    description: 'Completou 30 treinos'
  },
  {
    icon: Trophy,
    color: 'text-purple-500',
    title: 'Meta Atingida',
    description: 'Alcançou seu objetivo de peso'
  }
];

const mockFriends = [
  {
    id: '1',
    name: 'Maria Silva',
    photo: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=50&h=50&fit=crop',
    points: 2500,
    lastActive: '2024-03-14'
  },
  {
    id: '2',
    name: 'Pedro Santos',
    photo: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=50&h=50&fit=crop',
    points: 2350,
    lastActive: '2024-03-14'
  },
  {
    id: '3',
    name: 'Ana Costa',
    photo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop',
    points: 2100,
    lastActive: '2024-03-13'
  }
];

export function ProfilePage() {
  // const [pointsHistory, setPointsHistory] = React.useState<PointsHistoryType[]>([]);
  const [userData, setUserData] = React.useState<any>(null);
  const [loadingPage, setLoadingPage] = React.useState(false);
  const [isPlansModalOpen, setIsPlansModalOpen] = useState(false);

  useEffect(() => {
    const loadUserData = async () => {
      try {
        setLoadingPage(true);
      const userData = await axiosInstance.get('/users/me');
      if (userData.data.status === 'success') {
        setUserData(userData.data.data);
      }
      } catch (error) {
        console.error('Error loading user data:', error);
      } finally {
        setLoadingPage(false);
      }
    };

    loadUserData();
  }, []);

  /*
  React.useEffect(() => {
    async function loadPoints() {
      try {
        const data = await getUserPoints();
        setPointsHistory(data.history);
      } catch (error) {
        console.error('Error loading points:', error);
        setError(error instanceof Error ? error : new Error('Failed to load points'));
      }
    }

    loadPoints();
  }, []);
  */

  // Show empty history if there's an error
  // const displayHistory = error ? [] : pointsHistory;

  return (
    <>
    {loadingPage && (
    <LoadingOverlay />
    )}

    <div className="space-y-6">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">Perfil</h1>
        <Link
          to="/settings"
          className="flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <Settings className="w-5 h-5" />
          <span>Configurações</span>
        </Link>
      </div>

      <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6">
        <div className="flex items-center gap-4 mb-4">
          <img
            src={img_user_profile}
            alt="Profile"
            className="w-16 h-16 rounded-full object-cover"
          />
          <div>
            <h2 className="text-lg font-semibold text-gray-800">{userData?.name}</h2>
            {userData?.goal && (
              <p className="text-sm text-gray-600">Objetivo: {userData?.goal}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3 mb-4">
          {userData?.height && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="text-xs text-gray-600">Altura</div>
            <div className="text-base font-semibold">{userData?.height}cm</div>
          </div>
          )}
          {userData?.weight && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="text-xs text-gray-600">Peso Atual</div>
            <div className="text-base font-semibold">{userData?.weight}kg</div>
          </div>
          )}
        </div>

        <div className="bg-gray-50 rounded-lg p-3 mb-4">
          <h3 className="text-base font-semibold text-gray-800 mb-3">
            Snap Tokens
          </h3>

          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <SparkleIcon className="w-5 h-5 text-yellow-500" />
              </div>

              <div className="grid grid-cols-3 gap-4 w-full">
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-gray-600">Plano</span>
                  <span className="text-lg font-semibold">{userData?.tokens?.plan || 0}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-gray-600">Adquiridos</span>
                  <span className="text-lg font-semibold">{userData?.tokens?.earned || 0}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-gray-600">Promocional</span>
                  <span className="text-lg font-semibold">{userData?.tokens?.promotional || 0}</span>
                </div>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                className="flex-1 bg-gray-100 hover:bg-gray-200 rounded-lg p-3 text-sm font-medium text-gray-800 transition-colors"
                onClick={() => setIsPlansModalOpen(true)}
              >
                Meu Plano
              </button>
              <button className="flex-1 bg-gray-100 hover:bg-gray-200 rounded-lg p-3 text-sm font-medium text-gray-800 transition-colors">
                Obter Tokens
              </button>
            </div>
          </div>

        </div>

        <div className="space-y-4">
          <ProfessionalCard
            title="Meu Nutricionista"
            professional={mockProfessionals.nutritionist}
          />
          <ProfessionalCard
            title="Meu Coach"
            professional={mockProfessionals.coach}
          />
        </div>

        <div className="border-t pt-4 mt-4 opacity-50 cursor-default">
          <h3 className="text-base font-semibold text-gray-800 mb-3">
            Conquistas <BadgeComingSoon />
          </h3>
          <div className="grid gap-3">
            {achievements.map((achievement, index) => {
              const Icon = achievement.icon;
              return (
                <div
                  key={index}
                  className="flex items-center p-3 bg-gray-50 rounded-lg"
                >
                  <Icon className={`w-6 h-6 ${achievement.color} mr-3`} />
                  <div>
                    <div className="text-sm font-medium text-gray-800">
                      {achievement.title}
                    </div>
                    <div className="text-xs text-gray-600">
                      {achievement.description}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      { /*
      <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6 opacity-50 cursor-default">
        <PointsHistory history={pointsHistory} />
      </div>
      */ }

      <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6 opacity-50 cursor-default">
        <FriendsRanking
          friends={mockFriends}
          onInvite={() => {
            // Handle friend invite
            console.log('Invite friends');
          }}
        />
      </div>

      {/* Modal de Planos */}
      <PlansModal
        isOpen={isPlansModalOpen}
        onClose={() => setIsPlansModalOpen(false)}
      />
    </div>
    </>
  );
}