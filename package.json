{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tanstack/react-query": "^5.71.1", "@tanstack/react-query-devtools": "^5.71.2", "axios": "^1.7.9", "canvas-confetti": "^1.9.2", "currency.js": "^2.0.4", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "idb": "^8.0.0", "jspdf": "^2.5.1", "ky": "^1.7.5", "lucide-react": "^0.487.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-input-mask": "^2.0.4", "react-router-dom": "^6.22.3", "react-toastify": "^11.0.3", "recharts": "^2.12.2", "recordrtc": "^5.6.2", "rsuite": "^5.77.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/canvas-confetti": "^1.6.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/recordrtc": "^5.6.14", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}