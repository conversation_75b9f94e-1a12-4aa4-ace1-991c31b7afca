import { apiService } from './api';

export interface AdminTransaction {
  id: string;
  user_name: string;
  user_email: string;
  provider_transaction_id: string;
  amount: number;
  currency: string;
  status: string;
  source_type: string;
  source_id: string;
  created_at: string;
  payment_provider_name: string;
}

export interface AdminTransactionsResponse {
  status: string;
  data: AdminTransaction[];
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

export interface TransactionFilters {
  page?: number;
  limit?: number;
  status?: string;
  source_type?: string;
  payment_provider?: string;
  search?: string;
  start_date?: string;
  end_date?: string;
  min_amount?: number;
  max_amount?: number;
}

/**
 * Busca as transações administrativas
 * @param filters Filtros para a busca
 * @returns Lista de transações com paginação
 */
export const getAdminTransactions = async (
  filters: TransactionFilters = {}
): Promise<AdminTransactionsResponse> => {
  try {
    const queryParams = new URLSearchParams();
    
    // Adicionar parâmetros de paginação
    queryParams.append('page', (filters.page || 1).toString());
    queryParams.append('limit', (filters.limit || 10).toString());
    
    // Adicionar filtros opcionais
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.source_type) queryParams.append('source_type', filters.source_type);
    if (filters.payment_provider) queryParams.append('payment_provider', filters.payment_provider);
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.start_date) queryParams.append('start_date', filters.start_date);
    if (filters.end_date) queryParams.append('end_date', filters.end_date);
    if (filters.min_amount) queryParams.append('min_amount', filters.min_amount.toString());
    if (filters.max_amount) queryParams.append('max_amount', filters.max_amount.toString());

    const response = await apiService.get<AdminTransactionsResponse>(
      `admin/transactions?${queryParams.toString()}`
    );
    
    if (response.status === 'success') {
      return response;
    }
    
    throw new Error('Erro ao buscar transações');
  } catch (error) {
    console.error('Erro ao buscar transações administrativas:', error);
    throw error;
  }
};

/**
 * Busca uma transação específica por ID
 * @param transactionId ID da transação
 * @returns Dados da transação
 */
export const getAdminTransactionById = async (
  transactionId: string
): Promise<AdminTransaction> => {
  try {
    const response = await apiService.get<{ status: string; data: AdminTransaction }>(
      `admin/transactions/${transactionId}`
    );
    
    if (response.status === 'success' && response.data) {
      return response.data;
    }
    
    throw new Error('Transação não encontrada');
  } catch (error) {
    console.error('Erro ao buscar transação:', error);
    throw error;
  }
};

/**
 * Atualiza o status de uma transação
 * @param transactionId ID da transação
 * @param status Novo status
 * @returns Resposta da API
 */
export const updateTransactionStatus = async (
  transactionId: string,
  status: string
): Promise<any> => {
  try {
    const response = await apiService.put(
      `admin/transactions/${transactionId}/status`,
      { status }
    );
    
    return response;
  } catch (error) {
    console.error('Erro ao atualizar status da transação:', error);
    throw error;
  }
};

/**
 * Exporta transações para CSV
 * @param filters Filtros para exportação
 * @returns URL do arquivo CSV
 */
export const exportTransactions = async (
  filters: TransactionFilters = {}
): Promise<string> => {
  try {
    const queryParams = new URLSearchParams();
    
    // Adicionar filtros para exportação
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.source_type) queryParams.append('source_type', filters.source_type);
    if (filters.payment_provider) queryParams.append('payment_provider', filters.payment_provider);
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.start_date) queryParams.append('start_date', filters.start_date);
    if (filters.end_date) queryParams.append('end_date', filters.end_date);
    if (filters.min_amount) queryParams.append('min_amount', filters.min_amount.toString());
    if (filters.max_amount) queryParams.append('max_amount', filters.max_amount.toString());

    const response = await apiService.get<{ status: string; data: { url: string } }>(
      `admin/transactions/export?${queryParams.toString()}`
    );
    
    if (response.status === 'success' && response.data?.url) {
      return response.data.url;
    }
    
    throw new Error('Erro ao exportar transações');
  } catch (error) {
    console.error('Erro ao exportar transações:', error);
    throw error;
  }
};
