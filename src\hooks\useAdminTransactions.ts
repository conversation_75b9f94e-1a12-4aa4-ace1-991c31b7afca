import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import {
  getAdminTransactions,
  getAdminTransactionById,
  updateTransactionStatus,
  exportTransactions,
  TransactionFilters,
  AdminTransactionsResponse,
  AdminTransaction
} from '../services/adminTransactions';

/**
 * Hook para gerenciar transações administrativas
 * @returns Objeto com dados das transações e funções para interagir com elas
 */
export const useAdminTransactions = () => {
  const queryClient = useQueryClient();
  
  // Estados para filtros
  const [filters, setFilters] = useState<TransactionFilters>({
    page: 1,
    limit: 10,
    status: '',
    source_type: '',
    payment_provider: '',
    search: ''
  });

  // Query para buscar transações
  const {
    data: transactionsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['adminTransactions', filters],
    queryFn: () => getAdminTransactions(filters),
    keepPreviousData: true, // Mantém dados anteriores durante carregamento
    staleTime: 30 * 1000, // 30 segundos
  });

  // Mutation para atualizar status
  const updateStatusMutation = useMutation({
    mutationFn: ({ transactionId, status }: { transactionId: string; status: string }) =>
      updateTransactionStatus(transactionId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminTransactions'] });
      toast.success('Status da transação atualizado com sucesso!', {
        position: 'bottom-right'
      });
    },
    onError: (error) => {
      console.error('Erro ao atualizar status:', error);
      toast.error('Erro ao atualizar status. Tente novamente.', {
        position: 'bottom-right'
      });
    }
  });

  // Mutation para exportar transações
  const exportMutation = useMutation({
    mutationFn: (exportFilters: TransactionFilters) => exportTransactions(exportFilters),
    onSuccess: (url) => {
      // Abrir URL de download
      window.open(url, '_blank');
      toast.success('Exportação iniciada! O download começará em breve.', {
        position: 'bottom-right'
      });
    },
    onError: (error) => {
      console.error('Erro ao exportar:', error);
      toast.error('Erro ao exportar transações. Tente novamente.', {
        position: 'bottom-right'
      });
    }
  });

  /**
   * Atualiza os filtros de busca
   * @param newFilters Novos filtros
   */
  const updateFilters = (newFilters: Partial<TransactionFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      // Reset para primeira página quando mudamos filtros (exceto paginação)
      ...(newFilters.page === undefined && { page: 1 })
    }));
  };

  /**
   * Atualiza o status de uma transação
   * @param transactionId ID da transação
   * @param status Novo status
   */
  const updateStatus = (transactionId: string, status: string) => {
    updateStatusMutation.mutate({ transactionId, status });
  };

  /**
   * Exporta transações com os filtros atuais
   */
  const exportCurrentTransactions = () => {
    const exportFilters = { ...filters };
    delete exportFilters.page;
    delete exportFilters.limit;
    exportMutation.mutate(exportFilters);
  };

  /**
   * Vai para a próxima página
   */
  const nextPage = () => {
    if (transactionsData?.pagination && filters.page! < Math.ceil(transactionsData.pagination.total / filters.limit!)) {
      updateFilters({ page: (filters.page || 1) + 1 });
    }
  };

  /**
   * Vai para a página anterior
   */
  const previousPage = () => {
    if (filters.page! > 1) {
      updateFilters({ page: (filters.page || 1) - 1 });
    }
  };

  /**
   * Vai para uma página específica
   * @param page Número da página
   */
  const goToPage = (page: number) => {
    updateFilters({ page });
  };

  /**
   * Busca uma transação específica por ID
   * @param transactionId ID da transação
   */
  const getTransactionById = (transactionId: string) => {
    return useQuery({
      queryKey: ['adminTransaction', transactionId],
      queryFn: () => getAdminTransactionById(transactionId),
      enabled: !!transactionId,
      staleTime: 5 * 60 * 1000, // 5 minutos
    });
  };

  /**
   * Calcula estatísticas das transações
   */
  const getTransactionStats = () => {
    const transactions = transactionsData?.data || [];
    
    const totalAmount = transactions.reduce((sum, transaction) => sum + transaction.amount, 0);
    const successfulTransactions = transactions.filter(t => t.status === 'completed').length;
    const failedTransactions = transactions.filter(t => t.status === 'failed').length;
    const pendingTransactions = transactions.filter(t => t.status === 'pending').length;
    
    return {
      totalAmount,
      totalTransactions: transactions.length,
      successfulTransactions,
      failedTransactions,
      pendingTransactions,
      successRate: transactions.length > 0 ? (successfulTransactions / transactions.length) * 100 : 0
    };
  };

  return {
    // Dados
    transactions: transactionsData?.data || [],
    pagination: transactionsData?.pagination,
    filters,
    stats: getTransactionStats(),
    
    // Estados
    isLoading,
    error,
    isUpdatingStatus: updateStatusMutation.isPending,
    isExporting: exportMutation.isPending,
    
    // Funções
    updateFilters,
    updateStatus,
    exportCurrentTransactions,
    refetch,
    nextPage,
    previousPage,
    goToPage,
    getTransactionById
  };
};
