import React, { useEffect } from 'react';
import { useAffiliateLinkVisit } from '../hooks/useAffiliateLinkVisit';

interface AffiliateLinkTrackerProps {
  linkId: string;
  children: React.ReactNode;
  onClick?: () => void;
  trackOnMount?: boolean;
  trackOnClick?: boolean;
  customData?: {
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    utm_content?: string;
    utm_term?: string;
    referrer?: string;
  };
  className?: string;
}

/**
 * Componente wrapper que automaticamente rastreia cliques em links de afiliados
 */
export const AffiliateLinkTracker: React.FC<AffiliateLinkTrackerProps> = ({
  linkId,
  children,
  onClick,
  trackOnMount = false,
  trackOnClick = true,
  customData,
  className,
  ...props
}) => {
  const { 
    visitLinkWithTracking, 
    autoVisitLink, 
    isVisiting, 
    wasRecentlyVisited 
  } = useAffiliateLinkVisit();

  // Rastrear na montagem do componente se solicitado
  useEffect(() => {
    if (trackOnMount && linkId && !wasRecentlyVisited(linkId)) {
      if (customData) {
        visitLinkWithTracking(linkId, customData);
      } else {
        autoVisitLink(linkId);
      }
    }
  }, [linkId, trackOnMount, customData, visitLinkWithTracking, autoVisitLink, wasRecentlyVisited]);

  const handleClick = () => {
    // Rastrear clique se solicitado
    if (trackOnClick && linkId && !wasRecentlyVisited(linkId, 1)) { // 1 minuto para cliques
      if (customData) {
        visitLinkWithTracking(linkId, customData);
      } else {
        autoVisitLink(linkId);
      }
    }

    // Executar callback personalizado
    if (onClick) {
      onClick();
    }
  };

  return (
    <div 
      onClick={handleClick} 
      className={`${className} ${isVisiting ? 'opacity-75' : ''}`}
      {...props}
    >
      {children}
    </div>
  );
};

interface ShareButtonProps {
  linkId: string;
  shareUrl: string;
  platform: 'facebook' | 'twitter' | 'whatsapp' | 'telegram' | 'email' | 'copy';
  className?: string;
}

/**
 * Componente de botão de compartilhamento que rastreia cliques
 */
export const ShareButton: React.FC<ShareButtonProps> = ({
  linkId,
  shareUrl,
  platform,
  className = ''
}) => {
  const { visitLinkWithTracking } = useAffiliateLinkVisit();

  const handleShare = () => {
    // Rastrear o compartilhamento
    visitLinkWithTracking(linkId, {
      utm_source: platform,
      utm_medium: 'social',
      utm_campaign: 'affiliate_share'
    });

    // Executar ação de compartilhamento
    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`, '_blank');
        break;
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}`, '_blank');
        break;
      case 'whatsapp':
        window.open(`https://wa.me/?text=${encodeURIComponent(shareUrl)}`, '_blank');
        break;
      case 'telegram':
        window.open(`https://t.me/share/url?url=${encodeURIComponent(shareUrl)}`, '_blank');
        break;
      case 'email':
        window.location.href = `mailto:?subject=Convite&body=${encodeURIComponent(shareUrl)}`;
        break;
      case 'copy':
        navigator.clipboard.writeText(shareUrl);
        break;
    }
  };

  const platformConfig = {
    facebook: { label: 'Facebook', icon: '📘' },
    twitter: { label: 'Twitter', icon: '🐦' },
    whatsapp: { label: 'WhatsApp', icon: '💬' },
    telegram: { label: 'Telegram', icon: '✈️' },
    email: { label: 'Email', icon: '📧' },
    copy: { label: 'Copiar Link', icon: '📋' }
  };

  const config = platformConfig[platform];

  return (
    <button
      onClick={handleShare}
      className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${className}`}
    >
      <span>{config.icon}</span>
      <span>{config.label}</span>
    </button>
  );
};

interface AffiliateStatsDisplayProps {
  className?: string;
}

/**
 * Componente para exibir estatísticas de visitas armazenadas localmente
 */
export const AffiliateStatsDisplay: React.FC<AffiliateStatsDisplayProps> = ({ 
  className = '' 
}) => {
  const { getStoredVisitData } = useAffiliateLinkVisit();
  const visitData = getStoredVisitData();

  if (!visitData) {
    return null;
  }

  return (
    <div className={`bg-gray-50 p-4 rounded-lg ${className}`}>
      <h3 className="text-sm font-medium text-gray-900 mb-2">Estatísticas de Visitas</h3>
      
      {visitData.lastVisit && (
        <div className="mb-3">
          <p className="text-xs text-gray-600">Última visita:</p>
          <p className="text-sm text-gray-900">
            Link: {visitData.lastVisit.linkId}
          </p>
          <p className="text-xs text-gray-500">
            {new Date(visitData.lastVisit.visitedAt).toLocaleString('pt-BR')}
          </p>
        </div>
      )}
      
      {visitData.visits && visitData.visits.length > 0 && (
        <div>
          <p className="text-xs text-gray-600 mb-1">
            Total de visitas: {visitData.visits.length}
          </p>
          <div className="max-h-32 overflow-y-auto">
            {visitData.visits.slice(-5).map((visit: any, index: number) => (
              <div key={index} className="text-xs text-gray-500 py-1 border-b border-gray-200 last:border-b-0">
                {visit.linkId} - {new Date(visit.visitedAt).toLocaleTimeString('pt-BR')}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
