import { apiService } from './api';

export interface AffiliateData {
  name: string;
  email: string;
}

export interface AffiliateResponse {
  status: string;
  data: AffiliateData;
}

export interface Affiliate extends AffiliateData {
  id: string;
  status: string;
  is_master: boolean;
  accepted_at: string | null;
  created_at: string;
}

export interface AffiliateListResponse {
  status: string;
  data: Affiliate[];
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

const STORAGE_KEY = 'snapfit_affiliates';

/**
 * Busca os dados do afiliado atual
 * @returns Dados do afiliado
 */
export const getAffiliateData = async (): Promise<AffiliateData> => {
  try {
    // Verificar se já temos os dados no localStorage
    const storedData = localStorage.getItem(STORAGE_KEY);

    if (storedData) {
      // Se temos dados no localStorage, usamos eles
      return JSON.parse(storedData);
    }

    // Se não temos dados no localStorage, fazemos a requisição
    const response = await apiService.get<AffiliateResponse>('affiliates/me');

    if (response.status === 'success' && response.data) {
      // Armazenar os dados no localStorage
      localStorage.setItem(STORAGE_KEY, JSON.stringify(response.data));
      return response.data;
    }

    throw new Error('Dados de afiliado inválidos');
  } catch (error) {
    console.error('Erro ao buscar dados do afiliado:', error);
    throw error;
  }
};

/**
 * Limpa os dados do afiliado do localStorage
 */
export const clearAffiliateData = (): void => {
  localStorage.removeItem(STORAGE_KEY);
};

/**
 * Verifica se o usuário está autenticado como afiliado
 * @returns true se o usuário está autenticado como afiliado
 */
export const isAffiliateAuthenticated = (): boolean => {
  return !!localStorage.getItem(STORAGE_KEY);
};

/**
 * Busca a lista de afiliados (para administradores)
 * @param page Número da página
 * @param limit Limite de itens por página
 * @param filters Filtros adicionais
 * @returns Lista de afiliados
 */
export const getAffiliatesList = async (
  page: number = 1,
  limit: number = 10,
  filters: Record<string, string> = {}
): Promise<AffiliateListResponse> => {
  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...filters,
    });

    const response = await apiService.get<AffiliateListResponse>(
      `admin/affiliates?${queryParams.toString()}`
    );

    return response;
  } catch (error) {
    console.error('Erro ao buscar lista de afiliados:', error);
    throw error;
  }
};
