import React, { useState } from 'react';
import { AffiliateLinkTracker, ShareButton, AffiliateStatsDisplay } from '../components/AffiliateLinkTracker';
import { useAffiliateLinkVisit } from '../hooks/useAffiliateLinkVisit';

/**
 * Exemplo de como usar o sistema de rastreamento de links de afiliados
 */
export const AffiliateLinksExample: React.FC = () => {
  const [linkId, setLinkId] = useState('example-link-123');
  const [shareUrl, setShareUrl] = useState('https://snapfit.com/invite?ref=example-link-123');
  
  const { 
    visitLink, 
    visitLinkWithTracking, 
    autoVisitLink, 
    isVisiting, 
    getStoredVisitData,
    wasRecentlyVisited 
  } = useAffiliateLinkVisit();

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          Sistema de Rastreamento de Links de Afiliados
        </h1>

        {/* Controles de teste */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ID do Link
            </label>
            <input
              type="text"
              value={linkId}
              onChange={(e) => setLinkId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Digite o ID do link"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              URL de Compartilhamento
            </label>
            <input
              type="text"
              value={shareUrl}
              onChange={(e) => setShareUrl(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Digite a URL de compartilhamento"
            />
          </div>
        </div>

        {/* Botões de teste direto */}
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Testes Diretos</h3>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => visitLink(linkId)}
              disabled={isVisiting}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {isVisiting ? 'Visitando...' : 'Visita Simples'}
            </button>
            
            <button
              onClick={() => visitLinkWithTracking(linkId, { utm_source: 'test', utm_medium: 'button' })}
              disabled={isVisiting}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              {isVisiting ? 'Visitando...' : 'Visita com UTM'}
            </button>
            
            <button
              onClick={() => autoVisitLink(linkId)}
              disabled={isVisiting}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
            >
              {isVisiting ? 'Visitando...' : 'Auto Visita'}
            </button>
          </div>
          
          <div className="mt-3 text-sm text-gray-600">
            Status: {wasRecentlyVisited(linkId) ? '✅ Visitado recentemente' : '❌ Não visitado'}
          </div>
        </div>

        {/* Exemplo de link rastreado */}
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Link Rastreado Automaticamente</h3>
          <AffiliateLinkTracker
            linkId={linkId}
            trackOnClick={true}
            customData={{ utm_source: 'dashboard', utm_medium: 'click' }}
            className="inline-block"
          >
            <div className="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors cursor-pointer">
              🔗 Clique aqui para rastrear automaticamente
            </div>
          </AffiliateLinkTracker>
          <p className="text-sm text-gray-600 mt-2">
            Este link será rastreado automaticamente quando clicado
          </p>
        </div>

        {/* Botões de compartilhamento */}
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Compartilhamento Social</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            <ShareButton
              linkId={linkId}
              shareUrl={shareUrl}
              platform="facebook"
              className="bg-blue-600 text-white hover:bg-blue-700"
            />
            <ShareButton
              linkId={linkId}
              shareUrl={shareUrl}
              platform="twitter"
              className="bg-sky-500 text-white hover:bg-sky-600"
            />
            <ShareButton
              linkId={linkId}
              shareUrl={shareUrl}
              platform="whatsapp"
              className="bg-green-500 text-white hover:bg-green-600"
            />
            <ShareButton
              linkId={linkId}
              shareUrl={shareUrl}
              platform="telegram"
              className="bg-blue-500 text-white hover:bg-blue-600"
            />
            <ShareButton
              linkId={linkId}
              shareUrl={shareUrl}
              platform="email"
              className="bg-gray-600 text-white hover:bg-gray-700"
            />
            <ShareButton
              linkId={linkId}
              shareUrl={shareUrl}
              platform="copy"
              className="bg-gray-500 text-white hover:bg-gray-600"
            />
          </div>
          <p className="text-sm text-gray-600 mt-3">
            Cada compartilhamento será rastreado com UTM específico da plataforma
          </p>
        </div>

        {/* Estatísticas */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Estatísticas Locais</h3>
          <AffiliateStatsDisplay />
          
          <div className="mt-4">
            <button
              onClick={() => {
                const data = getStoredVisitData();
                console.log('Dados armazenados:', data);
                alert('Dados exibidos no console');
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Ver Dados no Console
            </button>
          </div>
        </div>
      </div>

      {/* Documentação */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Como Usar</h2>
        
        <div className="space-y-4 text-sm text-gray-700">
          <div>
            <h4 className="font-medium text-gray-900">1. Hook useAffiliateLinkVisit</h4>
            <p>Use o hook para ter controle total sobre o rastreamento:</p>
            <pre className="bg-gray-100 p-2 rounded mt-2 text-xs overflow-x-auto">
{`const { visitLink, autoVisitLink, isVisiting } = useAffiliateLinkVisit();

// Visita simples
visitLink('link-id');

// Visita automática com UTM
autoVisitLink('link-id');`}
            </pre>
          </div>

          <div>
            <h4 className="font-medium text-gray-900">2. Componente AffiliateLinkTracker</h4>
            <p>Wrapper que rastreia automaticamente cliques:</p>
            <pre className="bg-gray-100 p-2 rounded mt-2 text-xs overflow-x-auto">
{`<AffiliateLinkTracker linkId="123" trackOnClick={true}>
  <button>Meu Botão</button>
</AffiliateLinkTracker>`}
            </pre>
          </div>

          <div>
            <h4 className="font-medium text-gray-900">3. Componente ShareButton</h4>
            <p>Botões de compartilhamento com rastreamento:</p>
            <pre className="bg-gray-100 p-2 rounded mt-2 text-xs overflow-x-auto">
{`<ShareButton 
  linkId="123" 
  shareUrl="https://..." 
  platform="facebook" 
/>`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};
