import { useState, useRef, useEffect } from 'react'
import { FiMenu, FiX, FiUser, FiDollarSign, FiLink, FiPieChart, FiSettings, FiHelpCircle, FiBell, FiLogOut } from 'react-icons/fi'
import { useAffiliateAuth } from '../../../hooks/useAffiliateAuth'

// Import page components
import Dashboard from './pages/Dashboard'
import MyLinks from './pages/MyLinks'
import Earnings from './pages/Earnings'
import Settings from './pages/Settings'
import HelpCenter from './pages/HelpCenter'

// Define page types for type safety
type PageType = 'dashboard' | 'links' | 'earnings' | 'settings' | 'help'

const AffiliateArea = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [userDropdownOpen, setUserDropdownOpen] = useState(false)
  const [activePage, setActivePage] = useState<PageType>('dashboard')
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Usar o hook de autenticação de afiliado
  const { affiliate, isLoading, logout } = useAffiliateAuth()

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const toggleUserDropdown = () => {
    setUserDropdownOpen(!userDropdownOpen)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setUserDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Renderizar um indicador de carregamento enquanto verificamos a autenticação
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-600 border-r-transparent align-[-0.125em]"></div>
          <p className="mt-4 text-gray-700">Carregando área de afiliados...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden bg-gray-50">
      {/* Sidebar for mobile - overlay */}
      <div
        className={`fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity lg:hidden ${
          sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={toggleSidebar}
      />

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-sm transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-auto lg:z-auto lg:border-r lg:border-gray-200 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex items-center justify-between h-[64px] px-6">
          <div className="text-xl font-bold text-blue-600">SnapFit Afiliados</div>
          <button
            className="p-2 rounded-md text-gray-500 hover:text-gray-700 focus:outline-none lg:hidden"
            onClick={toggleSidebar}
          >
            <FiX className="h-6 w-6" />
          </button>
        </div>

        <nav className="mt-5 px-4">
          <div className="space-y-1">
            <button
              onClick={() => setActivePage('dashboard')}
              className={`flex w-full items-center px-4 py-3 text-sm font-medium rounded-md ${
                activePage === 'dashboard' ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <FiPieChart className="mr-3 h-5 w-5" />
              Dashboard
            </button>
            <button
              onClick={() => setActivePage('links')}
              className={`flex w-full items-center px-4 py-3 text-sm font-medium rounded-md ${
                activePage === 'links' ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <FiLink className="mr-3 h-5 w-5" />
              Meus Links
            </button>
            <button
              onClick={() => setActivePage('earnings')}
              className={`flex w-full items-center px-4 py-3 text-sm font-medium rounded-md ${
                activePage === 'earnings' ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <FiDollarSign className="mr-3 h-5 w-5" />
              Ganhos
            </button>
            {/* Botões ocultos para futuras implementações */}
            <button
              onClick={() => setActivePage('settings')}
              className="hidden w-full items-center px-4 py-3 text-sm font-medium rounded-md"
            >
              <FiSettings className="mr-3 h-5 w-5" />
              Configurações
            </button>
            <button
              onClick={() => setActivePage('help')}
              className="hidden w-full items-center px-4 py-3 text-sm font-medium rounded-md"
            >
              <FiHelpCircle className="mr-3 h-5 w-5" />
              Help Center
            </button>
          </div>
        </nav>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Topbar */}
        <header className="bg-white border-b border-gray-200 z-10">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-[64px]">
              <div className="flex items-center">
                <button
                  className="p-2 rounded-md text-gray-500 hover:text-gray-700 focus:outline-none lg:hidden"
                  onClick={toggleSidebar}
                >
                  <FiMenu className="h-6 w-6" />
                </button>
                <h1 className="ml-2 text-xl font-semibold text-gray-800 lg:hidden">Afiliados</h1>
              </div>
              <div className="flex items-center space-x-4">
                <button className="hidden p-1 text-gray-500 hover:text-gray-700 focus:outline-none">
                  <FiBell className="h-6 w-6" />
                </button>
                <div className="relative" ref={dropdownRef}>
                  <button
                    className="flex items-center text-sm rounded-full focus:outline-none"
                    onClick={toggleUserDropdown}
                  >
                    <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white">
                      <FiUser className="h-5 w-5" />
                    </div>
                  </button>

                  {/* User dropdown menu */}
                  {userDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 ring-1 ring-black ring-opacity-5">
                      {isLoading ? (
                        <div className="px-4 py-3 border-b">
                          <p className="text-sm font-medium text-gray-900">Carregando...</p>
                        </div>
                      ) : (
                        <div className="px-4 py-3 border-b">
                          <p className="text-sm font-medium text-gray-900">{affiliate?.name || 'Usuário'}</p>
                          <p className="text-xs text-gray-500 truncate">{affiliate?.email || 'Sem email'}</p>
                        </div>
                      )}
                      {/* Links ocultos para futuras implementações */}
                      <a
                        href="#"
                        className="hidden px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Meu Perfil
                      </a>
                      <a
                        href="#"
                        className="hidden px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Configurações
                      </a>
                      <button
                        onClick={logout}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <div className="flex items-center">
                          <FiLogOut className="mr-2 h-4 w-4" />
                          Sair
                        </div>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </header>



        {/* Main content area */}
        <main className="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8">
          {activePage === 'dashboard' && <Dashboard />}
          {activePage === 'links' && <MyLinks />}
          {activePage === 'earnings' && <Earnings />}
          {activePage === 'settings' && <Settings />}
          {activePage === 'help' && <HelpCenter />}
        </main>
      </div>
    </div>
  )
}

export default AffiliateArea
