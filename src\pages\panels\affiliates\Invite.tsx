import { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import LoadingOverlay from "../../../components/LoadingOverlay";
import { useAffiliateLinkVisit } from "../../../hooks/useAffiliateLinkVisit";

const Invite = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const ref = searchParams.get('ref');
  const [isProcessing, setIsProcessing] = useState(true);

  const { autoVisitLink, isVisiting, visitError } = useAffiliateLinkVisit();

  useEffect(() => {
    const processInvite = async () => {
      if (ref) {
        try {
          // Registrar a visita no link de afiliado
          autoVisitLink(ref);

          // Salvar dados do convite no localStorage
          const snapfitData = localStorage.getItem('snapfit_data');
          const data = snapfitData ? JSON.parse(snapfitData) : {};

          localStorage.setItem(
            'snapfit_data',
            JSON.stringify({
              ...data,
              invite: ref,
              inviteTimestamp: new Date().toISOString()
            })
          );

          // Aguardar um pouco para garantir que a requisição foi enviada
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
          console.error('Erro ao processar convite:', error);
        }
      }

      // Redirecionar para a página de registro
      setIsProcessing(false);
      navigate('/register', { replace: true });
    };

    processInvite();
  }, [ref, autoVisitLink, navigate]);

  // Mostrar loading enquanto processa
  if (isProcessing || isVisiting) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingOverlay />
          <div className="mt-4">
            <h2 className="text-lg font-medium text-gray-900">Processando convite...</h2>
            <p className="text-sm text-gray-600 mt-2">
              Você será redirecionado em instantes
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Fallback caso algo dê errado
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h2 className="text-lg font-medium text-gray-900">Redirecionando...</h2>
        {visitError && (
          <p className="text-sm text-red-600 mt-2">
            Houve um problema ao processar o convite, mas você será redirecionado normalmente.
          </p>
        )}
      </div>
    </div>
  );
}

export default Invite