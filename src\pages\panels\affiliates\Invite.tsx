import { useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import LoadingOverlay from "../../../components/LoadingOverlay";

const Invite = () => {
  const [searchParams] = useSearchParams();
  const ref = searchParams.get('ref');

  useEffect(() => {
    if (ref) {
      const snapfitData = localStorage.getItem('snapfit_data');
      const data = snapfitData ? JSON.parse(snapfitData) : {};
      
      localStorage.setItem(
        'snapfit_data',
        JSON.stringify({
          ...data,
          invite: ref
        })
      );
    }

    window.location.href = '/register';
  }, [ref]);

  return (
    <div>
      <LoadingOverlay />
    </div>
  )
}

export default Invite