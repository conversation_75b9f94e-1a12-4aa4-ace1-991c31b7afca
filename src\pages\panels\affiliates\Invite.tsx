import { useEffect, useRef } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import LoadingOverlay from "../../../components/LoadingOverlay";
import { useAffiliateLinkVisit } from "../../../hooks/useAffiliateLinkVisit";

const Invite = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const ref = searchParams.get('ref');
  const hasProcessed = useRef(false);

  const { visitLink } = useAffiliateLinkVisit();

  useEffect(() => {
    // Prevenir múltiplas execuções
    if (hasProcessed.current) return;
    hasProcessed.current = true;

    const processInvite = () => {
      if (ref) {
        // Registrar visita no link
        visitLink(ref);

        // Salvar dados do convite no localStorage
        const snapfitData = localStorage.getItem('snapfit_data');
        const data = snapfitData ? JSON.parse(snapfitData) : {};

        localStorage.setItem(
          'snapfit_data',
          JSON.stringify({
            ...data,
            invite: ref,
            inviteTimestamp: new Date().toISOString()
          })
        );
      }

      // Redirecionar para a página de registro
      navigate('/register', { replace: true });
    };

    // Pequeno delay para evitar execução imediata
    setTimeout(processInvite, 100);
  }, [ref, visitLink, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <LoadingOverlay />
        <div className="mt-4">
          <h2 className="text-lg font-medium text-gray-900">Processando convite...</h2>
          <p className="text-sm text-gray-600 mt-2">
            Você será redirecionado em instantes
          </p>
        </div>
      </div>
    </div>
  );
}

export default Invite