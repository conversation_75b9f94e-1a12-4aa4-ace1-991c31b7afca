import { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  getAffiliateOnboardingStatus, 
  updateAffiliateOnboardingStatus, 
  OnboardingStatus 
} from '../services/affiliate';

/**
 * Hook para gerenciar o status de onboarding do afiliado
 * @returns Objeto com o status de onboarding e funções para atualizá-lo
 */
export const useAffiliateOnboarding = () => {
  const [onboardingStatus, setOnboardingStatus] = useState<OnboardingStatus | null>(null);
  const queryClient = useQueryClient();

  // Usar React Query para buscar o status de onboarding
  const { 
    data: onboardingData, 
    isLoading, 
    error,
    refetch 
  } = useQuery({
    queryKey: ['affiliateOnboarding'],
    queryFn: getAffiliateOnboardingStatus,
    // Não refetch automaticamente, pois usamos o localStorage
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: Infinity,
  });

  // Atualizar o estado quando os dados forem carregados
  useEffect(() => {
    if (onboardingData) {
      setOnboardingStatus(onboardingData);
    }
  }, [onboardingData]);

  /**
   * Atualiza o status de onboarding
   * @param newStatus Novo status de onboarding
   */
  const updateStatus = (newStatus: OnboardingStatus) => {
    // Atualizar o localStorage
    updateAffiliateOnboardingStatus(newStatus);
    
    // Atualizar o estado local
    setOnboardingStatus(newStatus);
    
    // Invalidar a query para forçar um refetch na próxima vez
    queryClient.invalidateQueries({ queryKey: ['affiliateOnboarding'] });
  };

  return {
    onboardingStatus,
    isLoading,
    error,
    updateStatus,
    refetch
  };
};
