import React, { useState } from 'react';
import { Search, Filter, ArrowUpRight, Loader2, AlertCircle, ChevronLeft, ChevronRight, X } from 'lucide-react';
import { useAdminSubscriptions } from '../hooks/useAdminSubscriptions';
import type { AdminSubscription } from '../services/adminSubscriptions';

export function SubscriptionManagement() {
  // Estados locais para modal e busca
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<AdminSubscription | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Hook para gerenciar assinaturas
  const {
    subscriptions,
    pagination,
    filters,
    isLoading,
    error,
    isCanceling,
    updateFilters,
    cancelSubscription,
    nextPage,
    previousPage,
    goToPage
  } = useAdminSubscriptions();

  // Função para aplicar filtros de busca
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    updateFilters({ search: searchTerm });
  };

  // Função para cancelar assinatura
  const handleCancelSubscription = (immediate: boolean = false) => {
    if (selectedSubscription) {
      cancelSubscription(selectedSubscription.id, immediate);
      setShowCancelModal(false);
      setSelectedSubscription(null);
    }
  };

  // Função para formatar data
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  // Função para formatar preço
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  // Função para renderizar status
  const renderStatus = (status: string) => {
    const statusMap: Record<string, { label: string; className: string }> = {
      active: { label: 'Ativo', className: 'bg-green-100 text-green-800' },
      cancelled: { label: 'Cancelado', className: 'bg-red-100 text-red-800' },
      expired: { label: 'Expirado', className: 'bg-gray-100 text-gray-800' },
      pending: { label: 'Pendente', className: 'bg-yellow-100 text-yellow-800' },
      suspended: { label: 'Suspenso', className: 'bg-orange-100 text-orange-800' }
    };

    const statusInfo = statusMap[status] || { label: status, className: 'bg-gray-100 text-gray-800' };

    return (
      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusInfo.className}`}>
        {statusInfo.label}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 text-indigo-600 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-800">Gestão de Assinaturas</h1>
      </div>

      {error && (
        <div className="flex items-center gap-2 p-4 bg-red-50 text-red-600 rounded-lg">
          <AlertCircle className="w-5 h-5" />
          <span>{error instanceof Error ? error.message : 'Erro ao carregar assinaturas'}</span>
        </div>
      )}

      {/* Filtros e busca */}
      <div className="flex gap-4 flex-wrap">
        <form onSubmit={handleSearch} className="flex-1 min-w-[200px]">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar por nome, email ou plano..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </form>

        <div className="relative">
          <Filter className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <select
            value={filters.status || ''}
            onChange={(e) => updateFilters({ status: e.target.value || undefined })}
            className="pl-10 pr-4 py-2 border rounded-lg appearance-none bg-white min-w-[150px] focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="">Todos os status</option>
            <option value="active">Ativos</option>
            <option value="cancelled">Cancelados</option>
            <option value="expired">Expirados</option>
            <option value="pending">Pendentes</option>
            <option value="suspended">Suspensos</option>
          </select>
        </div>

        <div className="relative">
          <select
            value={filters.platform || ''}
            onChange={(e) => updateFilters({ platform: e.target.value || undefined })}
            className="px-4 py-2 border rounded-lg appearance-none bg-white min-w-[150px] focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="">Todas as plataformas</option>
            <option value="web">Web</option>
            <option value="ios">iOS</option>
            <option value="android">Android</option>
          </select>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usuário
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plano
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plataforma
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tokens
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data Início
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {subscriptions.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center text-gray-500">
                    Nenhuma assinatura encontrada
                  </td>
                </tr>
              ) : (
                subscriptions.map((subscription: AdminSubscription) => (
                  <tr key={subscription.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {subscription.user_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {subscription.user_email}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{subscription.plan_name}</div>
                      <div className="text-xs text-gray-500">
                        {subscription.payment_provider_name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {renderStatus(subscription.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                        {subscription.platform}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatPrice(subscription.price)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {subscription.snaptokens.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(subscription.start_date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button
                        onClick={() => {
                          setSelectedSubscription(subscription);
                          setShowCancelModal(true);
                        }}
                        className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
                        title="Ver detalhes"
                      >
                        <ArrowUpRight className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Paginação */}
        {pagination && pagination.total > 0 && (
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Mostrando <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> a{' '}
              <span className="font-medium">
                {Math.min(pagination.page * pagination.limit, pagination.total)}
              </span>{' '}
              de <span className="font-medium">{pagination.total}</span> resultados
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={previousPage}
                disabled={pagination.page === 1}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
              >
                <ChevronLeft className="h-4 w-4" />
                Anterior
              </button>

              {/* Números das páginas */}
              <div className="flex space-x-1">
                {Array.from({ length: Math.min(5, Math.ceil(pagination.total / pagination.limit)) }, (_, i) => {
                  const pageNumber = pagination.page - 2 + i;
                  if (pageNumber < 1 || pageNumber > Math.ceil(pagination.total / pagination.limit)) return null;

                  return (
                    <button
                      key={pageNumber}
                      onClick={() => goToPage(pageNumber)}
                      className={`px-3 py-1 text-sm font-medium rounded-md ${
                        pageNumber === pagination.page
                          ? 'bg-indigo-600 text-white'
                          : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNumber}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={nextPage}
                disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
              >
                Próximo
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modal de Detalhes/Cancelamento */}
      {showCancelModal && selectedSubscription && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-800">
                Detalhes da Assinatura
              </h2>
              <button
                onClick={() => {
                  setShowCancelModal(false);
                  setSelectedSubscription(null);
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Informações da assinatura */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Usuário</label>
                <p className="text-sm text-gray-900">{selectedSubscription.user_name}</p>
                <p className="text-xs text-gray-500">{selectedSubscription.user_email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Plano</label>
                <p className="text-sm text-gray-900">{selectedSubscription.plan_name}</p>
                <p className="text-xs text-gray-500">{selectedSubscription.payment_provider_name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                {renderStatus(selectedSubscription.status)}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Plataforma</label>
                <p className="text-sm text-gray-900">{selectedSubscription.platform}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Valor</label>
                <p className="text-sm text-gray-900">{formatPrice(selectedSubscription.price)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tokens</label>
                <p className="text-sm text-gray-900">{selectedSubscription.snaptokens.toLocaleString()}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Data de Início</label>
                <p className="text-sm text-gray-900">{formatDate(selectedSubscription.start_date)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Data de Fim</label>
                <p className="text-sm text-gray-900">{formatDate(selectedSubscription.end_date)}</p>
              </div>
            </div>

            {/* Ações de cancelamento */}
            {selectedSubscription.status === 'active' && (
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Cancelar Assinatura</h3>
                <p className="text-gray-600 mb-4">
                  Como você deseja cancelar esta assinatura?
                </p>
                <div className="space-y-3">
                  <button
                    onClick={() => handleCancelSubscription(false)}
                    disabled={isCanceling}
                    className="w-full px-4 py-3 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors text-left border border-gray-200"
                  >
                    <div className="font-medium">Ao final do período</div>
                    <div className="text-sm text-gray-500">
                      Mantém acesso até {formatDate(selectedSubscription.end_date)}
                    </div>
                  </button>
                  <button
                    onClick={() => handleCancelSubscription(true)}
                    disabled={isCanceling}
                    className="w-full px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors text-left border border-red-200"
                  >
                    <div className="font-medium">Imediatamente</div>
                    <div className="text-sm text-red-500">
                      Cancela o acesso agora
                    </div>
                  </button>
                </div>
              </div>
            )}

            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => {
                  setShowCancelModal(false);
                  setSelectedSubscription(null);
                }}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Fechar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}