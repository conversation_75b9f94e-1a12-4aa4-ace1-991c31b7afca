import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  getAffiliateData,
  clearAffiliateData,
  AffiliateData
} from '../services/affiliate';

export const useAffiliateAuth = () => {
  const [affiliate, setAffiliate] = useState<AffiliateData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const checkAffiliateAuth = async () => {
      try {
        const data = await getAffiliateData();
        setAffiliate(data);
      } catch (err) {
        console.error('Erro ao verificar autenticação de afiliado:', err);
        setError(err instanceof Error ? err : new Error('Erro desconhecido'));
        // Redirecionar para a página de login
        navigate('/login');
      } finally {
        setIsLoading(false);
      }
    };

    checkAffiliateAuth();
  }, [navigate]);

  // Função para fazer logout
  const logout = () => {
    /*
    clearAffiliateData();
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('deviceUid');
    navigate('/login');
    */
    localStorage.removeItem('snapfit_affiliates');
    navigate('/login');
  };

  return { affiliate, isLoading, error, logout };
};
