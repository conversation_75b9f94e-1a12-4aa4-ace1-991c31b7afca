import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { getUserPlans, createPlanCheckout, Plan } from '../services/plans';

/**
 * Hook para gerenciar os planos do usuário
 * @param enabled Se a query deve ser executada
 * @returns Objeto com dados dos planos e funções para interagir com eles
 */
export const usePlans = (enabled: boolean = true) => {
  const queryClient = useQueryClient();

  // Query para buscar os planos
  const {
    data: plans,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['userPlans'],
    queryFn: getUserPlans,
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutos
    cacheTime: 10 * 60 * 1000, // 10 minutos
  });

  // Mutation para criar checkout
  const checkoutMutation = useMutation({
    mutationFn: createPlanCheckout,
    onSuccess: (checkoutUrl) => {
      // Redirecionar para o Stripe
      window.location.href = checkoutUrl;
    },
    onError: (error) => {
      console.error('Erro ao criar checkout:', error);
      toast.error('Erro ao processar pagamento. Tente novamente.', {
        position: 'bottom-right'
      });
    }
  });

  /**
   * Inicia o processo de checkout para um plano
   * @param configId ID da configuração do plano
   */
  const selectPlan = (configId: string) => {
    checkoutMutation.mutate(configId);
  };

  /**
   * Busca o plano atualmente selecionado
   * @returns Plano selecionado ou null
   */
  const getSelectedPlan = (): Plan | null => {
    return plans?.find(plan => plan.selected) || null;
  };

  /**
   * Invalida o cache dos planos
   */
  const invalidatePlans = () => {
    queryClient.invalidateQueries({ queryKey: ['userPlans'] });
  };

  return {
    plans,
    isLoading,
    error,
    refetch,
    selectPlan,
    getSelectedPlan,
    invalidatePlans,
    isCheckingOut: checkoutMutation.isPending,
    checkoutError: checkoutMutation.error
  };
};
