import { useQuery } from '@tanstack/react-query';
import { getAffiliateDashboard, AffiliateDashboardData } from '../services/affiliateDashboard';

/**
 * Hook para gerenciar dados do dashboard do afiliado
 * @returns Objeto com dados do dashboard e estados de loading/erro
 */
export const useAffiliateDashboard = () => {
  const {
    data,
    isLoading,
    error,
    refetch,
    isRefetching
  } = useQuery({
    queryKey: ['affiliateDashboard'],
    queryFn: getAffiliateDashboard,
    staleTime: 5 * 60 * 1000, // 5 minutos
    cacheTime: 10 * 60 * 1000, // 10 minutos
    refetchOnWindowFocus: false,
  });

  return {
    // Dados
    dashboardData: data,
    totalClicks: data?.total_clicks || 0,
    totalEarnings: data?.total_earnings || 0,
    totalConversions: data?.total_conversions || 0,
    
    // Estados
    isLoading,
    error,
    isRefetching,
    
    // Funções
    refetch
  };
};
