import { useEffect } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import LoadingOverlay from "../../../components/LoadingOverlay"

const Join = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  useEffect(() => {
    try {
      const ref = searchParams.get('ref');
      const existingData = localStorage.getItem('snapfit_data');
      const newData = existingData 
        ? { ...JSON.parse(existingData), aff_join: true, ...(ref && { aff_ref: ref }) }
        : { aff_join: true, ...(ref && { aff_ref: ref }) };

      localStorage.setItem('snapfit_data', JSON.stringify(newData));
      navigate('/register');
    } catch (error) {
      console.error('Erro ao atualizar dados:', error);
      navigate('/register');
    }
  }, [navigate, searchParams]);

  return (
    <div>
      <LoadingOverlay />
    </div>
  )
}

export default Join