import { apiService } from './api';

export interface CommissionByStatus {
  status: string;
  count: number;
  total_value: number;
}

export interface LastPayment {
  value: number;
  date?: string;
}

export interface AffiliateDashboardData {
  total_clicks: number;
  total_earnings: number;
  total_conversions: number;
  commissions_by_status: CommissionByStatus[];
  last_payment: LastPayment;
}

export interface AffiliateDashboardResponse {
  status: string;
  data: AffiliateDashboardData;
}

/**
 * Busca os dados do dashboard do afiliado
 * @returns Dados do dashboard (cliques, ganhos, conversões)
 */
export const getAffiliateDashboard = async (): Promise<AffiliateDashboardData> => {
  try {
    const response = await apiService.get<AffiliateDashboardResponse>('affiliates/dashboard');

    if (response.status === 'success' && response.data) {
      return response.data;
    }

    throw new Error('Erro ao buscar dados do dashboard');
  } catch (error) {
    console.error('Erro ao buscar dashboard do afiliado:', error);
    throw error;
  }
};
