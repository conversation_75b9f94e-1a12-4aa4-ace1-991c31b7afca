import React from 'react';
import { X, <PERSON>, Sparkles, CreditCard } from 'lucide-react';
import { usePlans } from '../hooks/usePlans';

interface PlansModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PlansModal: React.FC<PlansModalProps> = ({ isOpen, onClose }) => {
  // Usar o hook personalizado para gerenciar os planos
  const {
    plans,
    isLoading,
    error,
    selectPlan,
    isCheckingOut,
    refetch
  } = usePlans(isOpen); // Só executa quando o modal está aberto

  const handleSelectPlan = (configId: string) => {
    selectPlan(configId);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900">Escolha seu Plano</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {isLoading ? (
            <div className="flex flex-col justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
              <p className="text-gray-600">Carregando planos disponíveis...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="text-red-500 mb-4">
                <X className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Erro ao carregar planos
              </h3>
              <p className="text-gray-600 mb-4">
                Não foi possível carregar os planos disponíveis. Tente novamente.
              </p>
              <button
                onClick={() => refetch()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Tentar Novamente
              </button>
            </div>
          ) : plans && plans.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {plans.map((plan) => (
                <div
                  key={plan.config_id}
                  className={`relative rounded-lg border-2 p-6 transition-all duration-200 ${
                    plan.selected
                      ? 'border-blue-500 bg-blue-50 shadow-lg'
                      : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                  }`}
                >
                  {/* Badge para plano selecionado */}
                  {plan.selected && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                        <Check className="h-4 w-4" />
                        Plano Atual
                      </div>
                    </div>
                  )}

                  {/* Conteúdo do plano */}
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      {plan.name}
                    </h3>

                    <div className="mb-4">
                      <span className="text-3xl font-bold text-gray-900">
                        {formatPrice(plan.price)}
                      </span>
                      <span className="text-gray-600 ml-1">/mês</span>
                    </div>

                    <p className="text-gray-600 mb-6 text-sm leading-relaxed">
                      {plan.description}
                    </p>

                    {/* Snap Tokens */}
                    <div className="bg-yellow-50 rounded-lg p-4 mb-6">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <Sparkles className="h-5 w-5 text-yellow-500" />
                        <span className="font-medium text-gray-900">Snap Tokens</span>
                      </div>
                      <div className="text-2xl font-bold text-yellow-600">
                        {plan?.snaptokens?.toLocaleString()}
                      </div>
                    </div>

                    {/* Botão de ação */}
                    <button
                      onClick={() => handleSelectPlan(plan.config_id)}
                      disabled={plan.selected || isCheckingOut}
                      className={`w-full py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 ${
                        plan.selected
                          ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                          : isCheckingOut
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-blue-600 text-white hover:bg-blue-700'
                      }`}
                    >
                      {isCheckingOut ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                          Processando...
                        </>
                      ) : plan.selected ? (
                        <>
                          <Check className="h-4 w-4" />
                          Plano Ativo
                        </>
                      ) : (
                        <>
                          <CreditCard className="h-4 w-4" />
                          Assinar Plano
                        </>
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Sparkles className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Nenhum plano disponível
              </h3>
              <p className="text-gray-600">
                Não há planos disponíveis no momento.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
