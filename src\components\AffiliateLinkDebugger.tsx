import React, { useState, useEffect } from 'react';
import { useAffiliateLinkVisit } from '../hooks/useAffiliateLinkVisit';

interface AffiliateLinkDebuggerProps {
  className?: string;
}

/**
 * Componente de debug para monitorar o sistema de rastreamento de links
 */
export const AffiliateLinkDebugger: React.FC<AffiliateLinkDebuggerProps> = ({ 
  className = '' 
}) => {
  const [linkId, setLinkId] = useState('debug-link-123');
  const [logs, setLogs] = useState<string[]>([]);
  
  const {
    visitLink,
    autoVisitLink,
    isVisiting,
    wasRecentlyVisited,
    getStoredVisitData,
    clearPendingVisitsCache,
    getPendingVisitsInfo,
    visitError
  } = useAffiliateLinkVisit();

  // Interceptar console.log para capturar logs do sistema
  useEffect(() => {
    const originalLog = console.log;
    console.log = (...args) => {
      const message = args.join(' ');
      if (message.includes('Link') || message.includes('Requisição') || message.includes('Visita')) {
        setLogs(prev => [...prev.slice(-9), `${new Date().toLocaleTimeString()}: ${message}`]);
      }
      originalLog(...args);
    };

    return () => {
      console.log = originalLog;
    };
  }, []);

  const addLog = (message: string) => {
    setLogs(prev => [...prev.slice(-9), `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleVisitLink = () => {
    addLog(`Tentando visitar link: ${linkId}`);
    visitLink(linkId);
  };

  const handleAutoVisitLink = () => {
    addLog(`Tentando auto-visitar link: ${linkId}`);
    autoVisitLink(linkId);
  };

  const handleClearCache = () => {
    clearPendingVisitsCache();
    addLog('Cache de requisições pendentes limpo');
  };

  const handleClearStorage = () => {
    localStorage.removeItem('snapfit_affiliate_visit');
    addLog('Dados do localStorage limpos');
  };

  const visitData = getStoredVisitData();
  const pendingInfo = getPendingVisitsInfo();
  const isRecentlyVisited = wasRecentlyVisited(linkId);

  return (
    <div className={`bg-gray-900 text-white p-4 rounded-lg font-mono text-sm ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold text-green-400">🔍 Affiliate Link Debugger</h3>
        <div className="flex gap-2">
          <button
            onClick={handleClearCache}
            className="px-2 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700"
          >
            Clear Cache
          </button>
          <button
            onClick={handleClearStorage}
            className="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700"
          >
            Clear Storage
          </button>
        </div>
      </div>

      {/* Controles */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-xs text-gray-300 mb-1">Link ID:</label>
          <input
            type="text"
            value={linkId}
            onChange={(e) => setLinkId(e.target.value)}
            className="w-full px-2 py-1 bg-gray-800 border border-gray-600 rounded text-white text-xs"
          />
        </div>
        
        <div className="flex gap-2 items-end">
          <button
            onClick={handleVisitLink}
            disabled={isVisiting}
            className="px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700 disabled:opacity-50"
          >
            {isVisiting ? 'Visiting...' : 'Visit Link'}
          </button>
          <button
            onClick={handleAutoVisitLink}
            disabled={isVisiting}
            className="px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700 disabled:opacity-50"
          >
            {isVisiting ? 'Visiting...' : 'Auto Visit'}
          </button>
        </div>
      </div>

      {/* Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div className="bg-gray-800 p-2 rounded">
          <div className="text-xs text-gray-300">Status:</div>
          <div className={`text-xs font-bold ${isRecentlyVisited ? 'text-green-400' : 'text-red-400'}`}>
            {isRecentlyVisited ? '✅ Recently Visited' : '❌ Not Visited'}
          </div>
        </div>
        
        <div className="bg-gray-800 p-2 rounded">
          <div className="text-xs text-gray-300">Pending Requests:</div>
          <div className="text-xs font-bold text-yellow-400">
            {pendingInfo.count} active
          </div>
        </div>
        
        <div className="bg-gray-800 p-2 rounded">
          <div className="text-xs text-gray-300">Request Status:</div>
          <div className={`text-xs font-bold ${isVisiting ? 'text-blue-400' : 'text-gray-400'}`}>
            {isVisiting ? '🔄 Processing' : '⏸️ Idle'}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {visitError && (
        <div className="bg-red-900 border border-red-600 p-2 rounded mb-4">
          <div className="text-xs text-red-300">Error:</div>
          <div className="text-xs text-red-100">{visitError.message}</div>
        </div>
      )}

      {/* Stored Data */}
      {visitData && (
        <div className="bg-gray-800 p-2 rounded mb-4">
          <div className="text-xs text-gray-300 mb-1">Stored Visit Data:</div>
          <div className="text-xs">
            <div>Last Visit: {visitData.lastVisit?.linkId || 'None'}</div>
            <div>Visit Time: {visitData.lastVisit?.visitedAt ? new Date(visitData.lastVisit.visitedAt).toLocaleString() : 'None'}</div>
            <div>Total Visits: {visitData.visits?.length || 0}</div>
          </div>
        </div>
      )}

      {/* Pending Requests Info */}
      {pendingInfo.count > 0 && (
        <div className="bg-yellow-900 border border-yellow-600 p-2 rounded mb-4">
          <div className="text-xs text-yellow-300 mb-1">Pending Requests:</div>
          <div className="text-xs">
            {pendingInfo.keys.map((key, index) => (
              <div key={index} className="text-yellow-100">• {key}</div>
            ))}
          </div>
        </div>
      )}

      {/* Logs */}
      <div className="bg-gray-800 p-2 rounded">
        <div className="text-xs text-gray-300 mb-1">System Logs:</div>
        <div className="max-h-32 overflow-y-auto">
          {logs.length === 0 ? (
            <div className="text-xs text-gray-500">No logs yet...</div>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="text-xs text-gray-100 py-1 border-b border-gray-700 last:border-b-0">
                {log}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-4 text-xs text-gray-400">
        <div className="font-bold mb-1">Instructions:</div>
        <div>• Click "Visit Link" multiple times quickly to test duplicate prevention</div>
        <div>• Check "Recently Visited" status to see if prevention is working</div>
        <div>• Monitor "Pending Requests" to see request deduplication</div>
        <div>• Use "Clear Cache" to reset pending requests</div>
        <div>• Use "Clear Storage" to reset visit history</div>
      </div>
    </div>
  );
};
